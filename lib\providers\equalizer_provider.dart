import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../services/equalizer_service.dart';
import '../services/ai_service.dart';

class EqualizerProvider extends ChangeNotifier {
  final EqualizerService _equalizerService = EqualizerService();
  final AIService _aiService = AIService();

  StreamSubscription<List<EqualizerBand>>? _bandsSubscription;
  StreamSubscription<Map<String, dynamic>>? _statusSubscription;
  StreamSubscription<MusicGenre>? _genreSubscription;
  StreamSubscription<Map<String, double>>? _confidenceSubscription;

  List<EqualizerBand> _bands = [];
  bool _isEnabled = true;
  bool _autoMode = false;
  double _masterGain = 0.0;
  String _currentPreset = 'Flat';

  // IA
  bool _aiAnalysisEnabled = false;
  MusicGenre _currentGenre = MusicGenre.unknown;
  Map<String, double> _genreConfidences = {};
  bool _isAnalyzing = false;

  // Visualisation
  final List<List<double>> _bandsHistory = [];
  static const int _maxHistoryLength = 50;

  // Getters
  List<EqualizerBand> get bands => List.unmodifiable(_bands);
  bool get isEnabled => _isEnabled;
  bool get autoMode => _autoMode;
  double get masterGain => _masterGain;
  String get currentPreset => _currentPreset;

  bool get aiAnalysisEnabled => _aiAnalysisEnabled;
  MusicGenre get currentGenre => _currentGenre;
  Map<String, double> get genreConfidences => Map.from(_genreConfidences);
  bool get isAnalyzing => _isAnalyzing;
  List<List<double>> get bandsHistory => List.from(_bandsHistory);

  EqualizerProvider() {
    _setupListeners();
    _initializeBands();
  }

  void _setupListeners() {
    _bandsSubscription = _equalizerService.bandsStream.listen(_onBandsUpdate);
    _statusSubscription = _equalizerService.statusStream.listen(_onStatusUpdate);
    _genreSubscription = _aiService.genreStream.listen(_onGenreUpdate);
    _confidenceSubscription = _aiService.confidenceStream.listen(_onConfidenceUpdate);
  }

  void _initializeBands() {
    _bands = _equalizerService.bands;
    notifyListeners();
  }

  void _onBandsUpdate(List<EqualizerBand> bands) {
    _bands = bands;

    // Mise à jour de l'historique
    final currentGains = _bands.map((band) => band.gain).toList();
    _bandsHistory.add(currentGains);
    if (_bandsHistory.length > _maxHistoryLength) {
      _bandsHistory.removeAt(0);
    }

    notifyListeners();
  }

  void _onStatusUpdate(Map<String, dynamic> status) {
    _isEnabled = status['enabled'] ?? true;
    _autoMode = status['autoMode'] ?? false;
    _masterGain = status['masterGain']?.toDouble() ?? 0.0;
    _currentPreset = status['currentPreset'] ?? 'Flat';
    notifyListeners();
  }

  void _onGenreUpdate(MusicGenre genre) {
    _currentGenre = genre;

    // Application automatique du preset si le mode auto est activé
    if (_autoMode && _aiAnalysisEnabled) {
      final aiPreset = _aiService.getEqualizerPreset(genre);
      final doublePreset = <String, double>{};
      aiPreset.forEach((key, value) {
        doublePreset[key] = value.toDouble();
      });
      _equalizerService.applyAIPreset(doublePreset);
    }

    notifyListeners();
  }

  void _onConfidenceUpdate(Map<String, double> confidences) {
    _genreConfidences = confidences;
    notifyListeners();
  }

  // Contrôles de l'égaliseur
  void setBandGain(int bandIndex, double gain) {
    _equalizerService.setBandGain(bandIndex, gain);
  }

  void setBandGainByName(String bandName, double gain) {
    _equalizerService.setBandGainByName(bandName, gain);
  }

  void setMasterGain(double gain) {
    _equalizerService.setMasterGain(gain);
  }

  void setEnabled(bool enabled) {
    _equalizerService.setEnabled(enabled);
  }

  void setAutoMode(bool auto) {
    _equalizerService.setAutoMode(auto);

    if (auto && _aiAnalysisEnabled) {
      startAIAnalysis();
    } else if (!auto) {
      stopAIAnalysis();
    }
  }

  void applyPreset(String presetName) {
    _equalizerService.applyPreset(presetName);
  }

  void resetToFlat() {
    _equalizerService.resetToFlat();
  }

  // Contrôles de l'IA
  void setAIAnalysisEnabled(bool enabled) {
    _aiAnalysisEnabled = enabled;

    if (enabled && _autoMode) {
      startAIAnalysis();
    } else {
      stopAIAnalysis();
    }

    notifyListeners();
  }

  Future<void> startAIAnalysis() async {
    if (_isAnalyzing) return;

    try {
      await _aiService.startAnalysis();
      _isAnalyzing = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du démarrage de l\'analyse IA: $e');
    }
  }

  void stopAIAnalysis() {
    _aiService.stopAnalysis();
    _isAnalyzing = false;
    notifyListeners();
  }

  // Méthodes utilitaires
  List<String> getAvailablePresets() {
    return _equalizerService.getAvailablePresets();
  }

  String getGenreName(MusicGenre genre) {
    return _aiService.getGenreName(genre);
  }

  String getCurrentGenreName() {
    return getGenreName(_currentGenre);
  }

  double getGenreConfidence(MusicGenre genre) {
    final genreName = getGenreName(genre);
    return _genreConfidences[genreName] ?? 0.0;
  }

  Color getBandColor(int bandIndex) {
    if (bandIndex < 0 || bandIndex >= _bands.length) return Colors.grey;

    final gain = _bands[bandIndex].gain;
    if (gain == 0) return Colors.blue;
    if (gain > 0) return Colors.green;
    return Colors.red;
  }

  Color getGenreConfidenceColor(double confidence) {
    if (confidence < 0.3) return Colors.red;
    if (confidence < 0.6) return Colors.orange;
    if (confidence < 0.8) return Colors.yellow;
    return Colors.green;
  }

  String getMasterGainDisplayText() {
    final sign = _masterGain >= 0 ? '+' : '';
    return '$sign${_masterGain.toStringAsFixed(1)} dB';
  }

  String getBandGainDisplayText(int bandIndex) {
    if (bandIndex < 0 || bandIndex >= _bands.length) return '0.0 dB';

    final gain = _bands[bandIndex].gain;
    final sign = gain >= 0 ? '+' : '';
    return '$sign${gain.toStringAsFixed(1)} dB';
  }

  // Analyse des tendances
  Map<String, double> getBandTrends() {
    if (_bandsHistory.length < 2) return {};

    final trends = <String, double>{};
    final recent = _bandsHistory.last;
    final previous = _bandsHistory[_bandsHistory.length - 2];

    for (int i = 0; i < _bands.length && i < recent.length && i < previous.length; i++) {
      final trend = recent[i] - previous[i];
      trends[_bands[i].name] = trend;
    }

    return trends;
  }

  List<double> getBandHistory(int bandIndex) {
    if (bandIndex < 0 || bandIndex >= _bands.length) return [];

    return _bandsHistory.map((gains) =>
      bandIndex < gains.length ? gains[bandIndex] : 0.0
    ).toList();
  }

  // Sauvegarde et restauration
  Map<String, dynamic> exportSettings() {
    return _equalizerService.exportSettings();
  }

  void importSettings(Map<String, dynamic> settings) {
    _equalizerService.importSettings(settings);
  }

  // Presets personnalisés
  void saveCustomPreset(String name) {
    // Implémentation future pour sauvegarder des presets personnalisés
    debugPrint('Sauvegarde du preset personnalisé: $name');
  }

  void deleteCustomPreset(String name) {
    // Implémentation future pour supprimer des presets personnalisés
    debugPrint('Suppression du preset personnalisé: $name');
  }

  void clearHistory() {
    _bandsHistory.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _bandsSubscription?.cancel();
    _statusSubscription?.cancel();
    _genreSubscription?.cancel();
    _confidenceSubscription?.cancel();
    _equalizerService.dispose();
    _aiService.dispose();
    super.dispose();
  }
}
