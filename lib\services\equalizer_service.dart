import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

class EqualizerBand {
  final String name;
  final double frequency;
  final double bandwidth;
  double gain;

  EqualizerBand({
    required this.name,
    required this.frequency,
    required this.bandwidth,
    this.gain = 0.0,
  });

  EqualizerBand copyWith({double? gain}) {
    return EqualizerBand(
      name: name,
      frequency: frequency,
      bandwidth: bandwidth,
      gain: gain ?? this.gain,
    );
  }
}

class EqualizerService {
  static const int sampleRate = 44100;

  final List<EqualizerBand> _bands = [
    EqualizerBand(name: 'Bass', frequency: 60, bandwidth: 40),
    EqualizerBand(name: 'Low Mid', frequency: 170, bandwidth: 100),
    EqualizerBand(name: 'Mid', frequency: 350, bandwidth: 200),
    EqualizerBand(name: 'High Mid', frequency: 1000, bandwidth: 500),
    EqualizerBand(name: 'Treble', frequency: 3500, bandwidth: 1500),
    EqualizerBand(name: 'Presence', frequency: 8000, bandwidth: 2000),
  ];

  StreamController<List<EqualizerBand>>? _bandsStreamController;
  StreamController<Map<String, dynamic>>? _statusStreamController;

  bool _isEnabled = true;
  bool _autoMode = false;
  double _masterGain = 0.0;
  String _currentPreset = 'Flat';

  // Filtres IIR pour chaque bande
  final List<_IIRFilter> _filters = [];

  Stream<List<EqualizerBand>> get bandsStream => _bandsStreamController!.stream;
  Stream<Map<String, dynamic>> get statusStream => _statusStreamController!.stream;

  List<EqualizerBand> get bands => List.unmodifiable(_bands);
  bool get isEnabled => _isEnabled;
  bool get autoMode => _autoMode;
  double get masterGain => _masterGain;
  String get currentPreset => _currentPreset;

  EqualizerService() {
    _bandsStreamController = StreamController<List<EqualizerBand>>.broadcast();
    _statusStreamController = StreamController<Map<String, dynamic>>.broadcast();
    _initializeFilters();
  }

  void _initializeFilters() {
    _filters.clear();
    for (final band in _bands) {
      _filters.add(_IIRFilter(
        frequency: band.frequency,
        sampleRate: sampleRate.toDouble(),
        q: band.frequency / band.bandwidth,
        gain: band.gain,
      ));
    }
  }

  Float32List processAudio(Float32List input) {
    if (!_isEnabled) return input;

    Float32List output = Float32List.fromList(input);

    // Application des filtres de chaque bande
    for (int i = 0; i < _filters.length; i++) {
      if (_bands[i].gain != 0.0) {
        output = _filters[i].process(output);
      }
    }

    // Application du gain maître
    if (_masterGain != 0.0) {
      final gainLinear = pow(10, _masterGain / 20).toDouble();
      for (int i = 0; i < output.length; i++) {
        output[i] *= gainLinear;
      }
    }

    // Limitation pour éviter la saturation
    for (int i = 0; i < output.length; i++) {
      output[i] = output[i].clamp(-1.0, 1.0);
    }

    return output;
  }

  void setBandGain(int bandIndex, double gain) {
    if (bandIndex < 0 || bandIndex >= _bands.length) return;

    gain = gain.clamp(-20.0, 20.0); // Limite ±20dB
    _bands[bandIndex] = _bands[bandIndex].copyWith(gain: gain);
    _filters[bandIndex].setGain(gain);

    _emitBandsUpdate();
    _emitStatusUpdate();
  }

  void setBandGainByName(String bandName, double gain) {
    final index = _bands.indexWhere((band) => band.name == bandName);
    if (index != -1) {
      setBandGain(index, gain);
    }
  }

  void setMasterGain(double gain) {
    _masterGain = gain.clamp(-20.0, 20.0);
    _emitStatusUpdate();
  }

  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    _emitStatusUpdate();
  }

  void setAutoMode(bool auto) {
    _autoMode = auto;
    _emitStatusUpdate();
  }

  void applyPreset(String presetName) {
    final preset = _getPreset(presetName);
    if (preset != null) {
      _currentPreset = presetName;
      _applyPresetValues(preset);
      _emitBandsUpdate();
      _emitStatusUpdate();
    }
  }

  void applyAIPreset(Map<String, double> aiPreset) {
    if (!_autoMode) return;

    aiPreset.forEach((bandName, gain) {
      setBandGainByName(bandName, gain * 10); // Conversion vers dB
    });

    _currentPreset = 'AI Auto';
  }

  void resetToFlat() {
    for (int i = 0; i < _bands.length; i++) {
      setBandGain(i, 0.0);
    }
    setMasterGain(0.0);
    _currentPreset = 'Flat';
  }

  Map<String, double>? _getPreset(String name) {
    final presets = {
      'Flat': {
        'Bass': 0.0,
        'Low Mid': 0.0,
        'Mid': 0.0,
        'High Mid': 0.0,
        'Treble': 0.0,
        'Presence': 0.0,
      },
      'Rock': {
        'Bass': 4.0,
        'Low Mid': 2.0,
        'Mid': -1.0,
        'High Mid': 2.0,
        'Treble': 3.0,
        'Presence': 2.0,
      },
      'Pop': {
        'Bass': 2.0,
        'Low Mid': 0.0,
        'Mid': 1.0,
        'High Mid': 2.0,
        'Treble': 3.0,
        'Presence': 1.0,
      },
      'Classical': {
        'Bass': 0.0,
        'Low Mid': 0.0,
        'Mid': 0.0,
        'High Mid': 1.0,
        'Treble': 2.0,
        'Presence': 1.0,
      },
      'Jazz': {
        'Bass': 1.0,
        'Low Mid': 2.0,
        'Mid': 1.0,
        'High Mid': 0.0,
        'Treble': 1.0,
        'Presence': 0.0,
      },
      'Electronic': {
        'Bass': 5.0,
        'Low Mid': 3.0,
        'Mid': -1.0,
        'High Mid': 1.0,
        'Treble': 4.0,
        'Presence': 2.0,
      },
      'Vocal': {
        'Bass': -2.0,
        'Low Mid': 0.0,
        'Mid': 3.0,
        'High Mid': 4.0,
        'Treble': 2.0,
        'Presence': 3.0,
      },
    };

    return presets[name];
  }

  void _applyPresetValues(Map<String, double> preset) {
    preset.forEach((bandName, gain) {
      setBandGainByName(bandName, gain);
    });
  }

  void _emitBandsUpdate() {
    _bandsStreamController!.add(List.from(_bands));
  }

  void _emitStatusUpdate() {
    _statusStreamController!.add({
      'enabled': _isEnabled,
      'autoMode': _autoMode,
      'masterGain': _masterGain,
      'currentPreset': _currentPreset,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  List<String> getAvailablePresets() {
    return [
      'Flat',
      'Rock',
      'Pop',
      'Classical',
      'Jazz',
      'Electronic',
      'Vocal',
    ];
  }

  Map<String, dynamic> exportSettings() {
    return {
      'bands': _bands.map((band) => {
        'name': band.name,
        'frequency': band.frequency,
        'bandwidth': band.bandwidth,
        'gain': band.gain,
      }).toList(),
      'masterGain': _masterGain,
      'enabled': _isEnabled,
      'autoMode': _autoMode,
      'currentPreset': _currentPreset,
    };
  }

  void importSettings(Map<String, dynamic> settings) {
    if (settings['bands'] != null) {
      final bandsData = settings['bands'] as List;
      for (int i = 0; i < bandsData.length && i < _bands.length; i++) {
        final bandData = bandsData[i];
        setBandGain(i, bandData['gain']?.toDouble() ?? 0.0);
      }
    }

    if (settings['masterGain'] != null) {
      setMasterGain(settings['masterGain'].toDouble());
    }

    if (settings['enabled'] != null) {
      setEnabled(settings['enabled']);
    }

    if (settings['autoMode'] != null) {
      setAutoMode(settings['autoMode']);
    }

    if (settings['currentPreset'] != null) {
      _currentPreset = settings['currentPreset'];
    }
  }

  void dispose() {
    _bandsStreamController?.close();
    _statusStreamController?.close();
  }
}

// Filtre IIR simple pour l'égalisation
class _IIRFilter {
  double frequency;
  double sampleRate;
  double q;
  double gain;

  // Coefficients du filtre
  double _b0 = 1.0, _b1 = 0.0, _b2 = 0.0;
  double _a1 = 0.0, _a2 = 0.0;

  // États du filtre
  double _x1 = 0.0, _x2 = 0.0;
  double _y1 = 0.0, _y2 = 0.0;

  _IIRFilter({
    required this.frequency,
    required this.sampleRate,
    required this.q,
    required this.gain,
  }) {
    _calculateCoefficients();
  }

  void setGain(double newGain) {
    gain = newGain;
    _calculateCoefficients();
  }

  void _calculateCoefficients() {
    // Filtre peaking EQ (bell filter)
    final w = 2 * pi * frequency / sampleRate;
    final cosw = cos(w);
    final sinw = sin(w);
    final A = pow(10, gain / 40); // gain en dB vers amplitude
    final alpha = sinw / (2 * q);

    final b0 = 1 + alpha * A;
    final b1 = -2 * cosw;
    final b2 = 1 - alpha * A;
    final a0 = 1 + alpha / A;
    final a1 = -2 * cosw;
    final a2 = 1 - alpha / A;

    // Normalisation
    _b0 = b0 / a0;
    _b1 = b1 / a0;
    _b2 = b2 / a0;
    _a1 = a1 / a0;
    _a2 = a2 / a0;
  }

  Float32List process(Float32List input) {
    final output = Float32List(input.length);

    for (int i = 0; i < input.length; i++) {
      final x0 = input[i];

      // Équation de différence du filtre IIR
      final y0 = _b0 * x0 + _b1 * _x1 + _b2 * _x2 - _a1 * _y1 - _a2 * _y2;

      output[i] = y0;

      // Mise à jour des états
      _x2 = _x1;
      _x1 = x0;
      _y2 = _y1;
      _y1 = y0;
    }

    return output;
  }

  void reset() {
    _x1 = _x2 = _y1 = _y2 = 0.0;
  }
}
