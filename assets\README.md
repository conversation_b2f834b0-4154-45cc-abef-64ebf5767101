# Assets Directory

Ce dossier contient les ressources de l'application AI Equalizer.

## Structure

```
assets/
├── images/          # Images et icônes
├── models/          # Modèles IA (TensorFlow Lite)
├── audio/           # Fichiers audio de test
└── fonts/           # Polices personnalisées (optionnel)
```

## Images
- Icônes pour les différents genres musicaux
- Logo de l'application
- Images pour l'interface utilisateur

## Modèles IA
- Modèles TensorFlow Lite pour la classification musicale
- Fichiers .tflite pré-entraînés
- Métadonnées des modèles

## Audio
- Échantillons audio pour les tests
- Fichiers de calibration
- Sons de notification (optionnel)

## Fonts
- Polices personnalisées si nécessaire
- Actuellement utilise Roboto par défaut

## Note
Les assets sont automatiquement inclus dans l'application lors de la compilation grâce à la configuration dans `pubspec.yaml`.
