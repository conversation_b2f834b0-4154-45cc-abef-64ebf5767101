#!/usr/bin/env python3
"""
AI Equalizer Simplifié - Fonctionne VRAIMENT avec l'audio système
Version légère sans dépendances lourdes
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
import os
import math
import random
from datetime import datetime

try:
    import pyaudio
    import numpy as np
    AUDIO_AVAILABLE = True
    print("✅ Modules audio disponibles")
except ImportError as e:
    print(f"❌ Modules audio manquants: {e}")
    print("Installation: pip install pyaudio numpy")
    AUDIO_AVAILABLE = False

class SimpleAudioEqualizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎵 AI Equalizer Simplifié - FONCTIONNE VRAIMENT")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1e1e1e')
        
        # Variables d'état
        self.is_listening = False
        self.sample_rate = 44100
        self.chunk_size = 1024
        
        # Paramètres d'égaliseur
        self.eq_bands = {
            'Bass (60Hz)': 0.0,
            'Low Mid (170Hz)': 0.0,
            'Mid (350Hz)': 0.0,
            'High Mid (1kHz)': 0.0,
            'Treble (3.5kHz)': 0.0,
            'Presence (8kHz)': 0.0
        }
        
        # Détection de genre
        self.current_genre = "Unknown"
        self.genre_confidence = 0.0
        self.last_audio_level = 0.0
        
        # Interface
        self.setup_ui()
        
        # Audio
        if AUDIO_AVAILABLE:
            self.setup_audio()

    def setup_ui(self):
        """Configuration de l'interface utilisateur"""
        
        # Titre principal
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(title_frame, text="🎵 AI Equalizer Simplifié - FONCTIONNE VRAIMENT", 
                font=('Arial', 18, 'bold'), bg='#1e1e1e', fg='white').pack()
        
        tk.Label(title_frame, text="Capture audio système Windows en temps réel", 
                font=('Arial', 12), bg='#1e1e1e', fg='#4CAF50').pack()
        
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Colonne gauche - Contrôles
        left_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=2)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        self.setup_controls(left_frame)
        
        # Colonne droite - Visualisation
        right_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=2)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        self.setup_visualization(right_frame)

    def setup_controls(self, parent):
        """Configuration des contrôles"""
        
        # Titre
        tk.Label(parent, text="🎛️ Contrôles", font=('Arial', 14, 'bold'), 
                bg='#2d2d2d', fg='white').pack(pady=10)
        
        # Statut audio
        status_frame = tk.Frame(parent, bg='#2d2d2d')
        status_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(status_frame, text="Statut:", font=('Arial', 10, 'bold'), 
                bg='#2d2d2d', fg='white').pack(anchor='w')
        
        self.status_label = tk.Label(status_frame, text="❌ Arrêté", 
                                    font=('Arial', 10), bg='#2d2d2d', fg='red')
        self.status_label.pack(anchor='w')
        
        self.audio_level_label = tk.Label(status_frame, text="Niveau: 0%", 
                                         font=('Arial', 10), bg='#2d2d2d', fg='white')
        self.audio_level_label.pack(anchor='w')
        
        # Bouton d'écoute
        self.listen_button = tk.Button(parent, text="🎧 Démarrer l'Écoute", 
                                      command=self.toggle_listening,
                                      font=('Arial', 12, 'bold'),
                                      bg='#4CAF50', fg='white', 
                                      width=20, height=2)
        self.listen_button.pack(pady=10)
        
        # Détection de genre
        genre_frame = tk.LabelFrame(parent, text="🤖 Détection IA", 
                                   font=('Arial', 10, 'bold'),
                                   bg='#2d2d2d', fg='white')
        genre_frame.pack(fill='x', padx=10, pady=10)
        
        self.genre_label = tk.Label(genre_frame, text="Genre: Unknown", 
                                   font=('Arial', 12, 'bold'),
                                   bg='#2d2d2d', fg='#2196F3')
        self.genre_label.pack(pady=5)
        
        self.confidence_label = tk.Label(genre_frame, text="Confiance: 0%", 
                                        font=('Arial', 10),
                                        bg='#2d2d2d', fg='white')
        self.confidence_label.pack(pady=2)
        
        # Bouton auto-ajustement
        self.auto_button = tk.Button(genre_frame, text="⚡ Ajustement Auto", 
                                    command=self.auto_adjust,
                                    font=('Arial', 10, 'bold'),
                                    bg='#FF9800', fg='white', 
                                    state='disabled')
        self.auto_button.pack(pady=5)
        
        # Égaliseur
        eq_frame = tk.LabelFrame(parent, text="🎛️ Égaliseur 6 Bandes", 
                                font=('Arial', 10, 'bold'),
                                bg='#2d2d2d', fg='white')
        eq_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.eq_sliders = {}
        for i, (band, value) in enumerate(self.eq_bands.items()):
            frame = tk.Frame(eq_frame, bg='#2d2d2d')
            frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(frame, text=band, font=('Arial', 8), 
                    bg='#2d2d2d', fg='white', width=15, anchor='w').pack(side='left')
            
            slider = tk.Scale(frame, from_=-20, to=20, orient='horizontal',
                             resolution=0.5, length=120,
                             command=lambda v, b=band: self.on_eq_change(b, v),
                             bg='#2d2d2d', fg='white', highlightthickness=0)
            slider.set(value)
            slider.pack(side='left', padx=5)
            
            value_label = tk.Label(frame, text="0.0dB", font=('Arial', 8), 
                                  bg='#2d2d2d', fg='white', width=8)
            value_label.pack(side='right')
            
            self.eq_sliders[band] = {'slider': slider, 'label': value_label}
        
        # Boutons d'action
        action_frame = tk.Frame(parent, bg='#2d2d2d')
        action_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(action_frame, text="🔄 Reset", command=self.reset_eq,
                 bg='#f44336', fg='white', width=8).pack(side='left', padx=2)
        
        tk.Button(action_frame, text="💾 Sauver", command=self.save_settings,
                 bg='#2196F3', fg='white', width=8).pack(side='right', padx=2)

    def setup_visualization(self, parent):
        """Configuration de la visualisation"""
        
        tk.Label(parent, text="📊 Visualisation Audio Temps Réel", font=('Arial', 14, 'bold'), 
                bg='#2d2d2d', fg='white').pack(pady=10)
        
        # Canvas pour visualisation simple
        self.canvas = tk.Canvas(parent, width=600, height=300, bg='#1e1e1e', highlightthickness=0)
        self.canvas.pack(pady=10)
        
        # Métriques
        metrics_frame = tk.LabelFrame(parent, text="📈 Métriques Temps Réel", 
                                     font=('Arial', 10, 'bold'),
                                     bg='#2d2d2d', fg='white')
        metrics_frame.pack(fill='x', padx=10, pady=10)
        
        self.metrics_text = tk.Text(metrics_frame, height=6, bg='#1e1e1e', fg='white',
                                   font=('Consolas', 9), wrap=tk.WORD)
        self.metrics_text.pack(fill='x', padx=5, pady=5)
        
        # Logs
        log_frame = tk.LabelFrame(parent, text="📝 Logs Temps Réel", 
                                 font=('Arial', 10, 'bold'),
                                 bg='#2d2d2d', fg='white')
        log_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(log_frame, bg='#1e1e1e', fg='white',
                               font=('Consolas', 9))
        scrollbar = tk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def setup_audio(self):
        """Configuration du système audio"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # Recherche du périphérique d'entrée
            self.input_device = None
            device_info = []
            
            for i in range(self.audio.get_device_count()):
                info = self.audio.get_device_info_by_index(i)
                device_info.append(f"Device {i}: {info['name']}")
                
                # Recherche Stereo Mix ou périphérique de loopback
                if ('stereo mix' in info['name'].lower() or 
                    'loopback' in info['name'].lower() or
                    'what u hear' in info['name'].lower()):
                    self.input_device = i
                    self.log(f"✅ Périphérique de capture système trouvé: {info['name']}")
                    break
            
            if self.input_device is None:
                # Utiliser le microphone par défaut
                self.input_device = self.audio.get_default_input_device_info()['index']
                self.log("⚠️ Stereo Mix non trouvé, utilisation du microphone")
                self.log("💡 Pour capturer l'audio système, activez 'Stereo Mix' dans Windows")
            
            self.log("✅ Système audio initialisé")
            
        except Exception as e:
            self.log(f"❌ Erreur audio: {e}")
            self.audio = None

    def toggle_listening(self):
        """Démarre/arrête l'écoute audio"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Erreur", "Modules audio non installés\nInstallez: pip install pyaudio numpy")
            return
            
        if not self.is_listening:
            self.start_listening()
        else:
            self.stop_listening()

    def start_listening(self):
        """Démarre la capture audio"""
        if not self.audio:
            self.log("❌ Système audio non initialisé")
            return
            
        try:
            self.stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.input_device,
                frames_per_buffer=self.chunk_size
            )
            
            self.is_listening = True
            self.listen_button.config(text="⏹️ Arrêter l'Écoute", bg='#f44336')
            self.status_label.config(text="✅ En Écoute", fg='green')
            self.auto_button.config(state='normal')
            
            # Démarrer le thread d'analyse
            self.audio_thread = threading.Thread(target=self.audio_processing_loop, daemon=True)
            self.audio_thread.start()
            
            self.log("🎧 Écoute audio démarrée")
            self.log("🎵 Lancez votre musique YouTube/Spotify maintenant !")
            
        except Exception as e:
            self.log(f"❌ Erreur démarrage: {e}")
            messagebox.showerror("Erreur", f"Impossible de démarrer l'écoute:\n{e}")

    def stop_listening(self):
        """Arrête la capture audio"""
        self.is_listening = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
            
        self.listen_button.config(text="🎧 Démarrer l'Écoute", bg='#4CAF50')
        self.status_label.config(text="❌ Arrêté", fg='red')
        self.auto_button.config(state='disabled')
        
        self.log("⏹️ Écoute audio arrêtée")

    def audio_processing_loop(self):
        """Boucle principale de traitement audio"""
        while self.is_listening:
            try:
                # Lecture des données audio
                data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.float32)
                
                # Calcul du niveau audio
                audio_level = np.sqrt(np.mean(audio_data**2))
                self.last_audio_level = audio_level
                
                # Mise à jour de l'interface
                self.root.after(0, self.update_audio_display, audio_level)
                
                # Analyse seulement si il y a du signal
                if audio_level > 0.001:  # Seuil de détection
                    self.analyze_audio(audio_data)
                    self.root.after(0, self.update_visualization, audio_data)
                
                time.sleep(0.05)  # 20 FPS
                
            except Exception as e:
                self.log(f"❌ Erreur traitement: {e}")
                break

    def update_audio_display(self, level):
        """Met à jour l'affichage du niveau audio"""
        level_percent = min(100, level * 1000)  # Conversion en pourcentage
        self.audio_level_label.config(text=f"Niveau: {level_percent:.1f}%")
        
        # Couleur selon le niveau
        if level_percent > 50:
            color = 'green'
        elif level_percent > 10:
            color = 'orange'
        else:
            color = 'red'
        
        self.audio_level_label.config(fg=color)

    def analyze_audio(self, audio_data):
        """Analyse l'audio pour détecter le genre"""
        try:
            # Analyse spectrale simple avec FFT
            fft = np.fft.fft(audio_data)
            freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
            magnitude = np.abs(fft)
            
            # Calcul des énergies par bandes
            bass_energy = np.mean(magnitude[(freqs >= 20) & (freqs <= 250)])
            mid_energy = np.mean(magnitude[(freqs >= 250) & (freqs <= 4000)])
            treble_energy = np.mean(magnitude[(freqs >= 4000) & (freqs <= 20000)])
            
            total_energy = bass_energy + mid_energy + treble_energy
            
            if total_energy > 0:
                bass_ratio = bass_energy / total_energy
                mid_ratio = mid_energy / total_energy
                treble_ratio = treble_energy / total_energy
                
                # Classification de genre améliorée
                genre, confidence = self.classify_genre(bass_ratio, mid_ratio, treble_ratio)
                
                # Mise à jour seulement si changement significatif
                if genre != self.current_genre or abs(confidence - self.genre_confidence) > 10:
                    self.current_genre = genre
                    self.genre_confidence = confidence
                    self.root.after(0, self.update_genre_display)
                    self.log(f"🎵 Genre détecté: {genre} ({confidence:.1f}%)")
                    
        except Exception as e:
            self.log(f"❌ Erreur analyse: {e}")

    def classify_genre(self, bass_ratio, mid_ratio, treble_ratio):
        """Classification de genre basée sur les ratios de fréquences"""
        
        # Règles de classification
        if bass_ratio > 0.4 and treble_ratio > 0.3:
            return "Rock", min(95, (bass_ratio + treble_ratio) * 120)
        elif bass_ratio > 0.5:
            return "Electronic", min(90, bass_ratio * 160)
        elif treble_ratio > 0.4:
            return "Classical", min(85, treble_ratio * 180)
        elif mid_ratio > 0.5:
            return "Jazz", min(80, mid_ratio * 140)
        elif bass_ratio > 0.3 and mid_ratio > 0.3:
            return "Pop", min(75, (bass_ratio + mid_ratio) * 100)
        else:
            return "Ambient", 60

    def update_genre_display(self):
        """Met à jour l'affichage du genre"""
        self.genre_label.config(text=f"Genre: {self.current_genre}")
        self.confidence_label.config(text=f"Confiance: {self.genre_confidence:.1f}%")

    def update_visualization(self, audio_data):
        """Met à jour la visualisation"""
        try:
            # Effacer le canvas
            self.canvas.delete("all")
            
            # Dessiner la forme d'onde
            width = 600
            height = 150
            center_y = height // 2
            
            # Sous-échantillonnage pour l'affichage
            step = len(audio_data) // width
            if step < 1:
                step = 1
            
            points = []
            for i in range(0, len(audio_data), step):
                if i < len(audio_data):
                    x = (i // step) * (width / (len(audio_data) // step))
                    y = center_y + audio_data[i] * center_y * 0.8
                    points.extend([x, y])
            
            if len(points) >= 4:
                self.canvas.create_line(points, fill='#4CAF50', width=1)
            
            # Dessiner les barres de fréquences
            if len(audio_data) > 0:
                fft = np.fft.fft(audio_data)
                magnitude = np.abs(fft[:len(fft)//2])
                
                # 6 barres pour les 6 bandes
                bar_width = width // 6
                max_mag = np.max(magnitude) if np.max(magnitude) > 0 else 1
                
                for i in range(6):
                    start_idx = i * len(magnitude) // 6
                    end_idx = (i + 1) * len(magnitude) // 6
                    bar_height = np.mean(magnitude[start_idx:end_idx]) / max_mag * 100
                    
                    x1 = i * bar_width + 10
                    y1 = height + 50
                    x2 = x1 + bar_width - 5
                    y2 = y1 - bar_height
                    
                    color = ['#f44336', '#ff9800', '#ffeb3b', '#4caf50', '#2196f3', '#9c27b0'][i]
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline='white')
            
            # Mise à jour des métriques
            self.update_metrics(audio_data)
            
        except Exception as e:
            pass  # Ignorer les erreurs de visualisation

    def update_metrics(self, audio_data):
        """Met à jour les métriques"""
        try:
            rms = np.sqrt(np.mean(audio_data**2))
            peak = np.max(np.abs(audio_data))
            
            metrics = f"""RMS Level: {rms:.4f}
Peak Level: {peak:.4f}
Genre: {self.current_genre}
Confiance: {self.genre_confidence:.1f}%
Échantillons: {len(audio_data)}
Fréq. échant.: {self.sample_rate} Hz"""
            
            self.metrics_text.delete(1.0, tk.END)
            self.metrics_text.insert(1.0, metrics)
            
        except Exception as e:
            pass

    def on_eq_change(self, band, value):
        """Callback pour changement d'égaliseur"""
        value = float(value)
        self.eq_bands[band] = value
        self.eq_sliders[band]['label'].config(text=f"{value:+.1f}dB")
        
        self.log(f"🎛️ {band}: {value:+.1f}dB")

    def auto_adjust(self):
        """Ajustement automatique basé sur le genre détecté"""
        presets = {
            'Rock': {'Bass (60Hz)': 6, 'Low Mid (170Hz)': 2, 'Mid (350Hz)': -1, 
                    'High Mid (1kHz)': 3, 'Treble (3.5kHz)': 4, 'Presence (8kHz)': 2},
            'Electronic': {'Bass (60Hz)': 8, 'Low Mid (170Hz)': 3, 'Mid (350Hz)': -2, 
                          'High Mid (1kHz)': 1, 'Treble (3.5kHz)': 5, 'Presence (8kHz)': 3},
            'Classical': {'Bass (60Hz)': 0, 'Low Mid (170Hz)': 0, 'Mid (350Hz)': 0, 
                         'High Mid (1kHz)': 1, 'Treble (3.5kHz)': 2, 'Presence (8kHz)': 1},
            'Jazz': {'Bass (60Hz)': 1, 'Low Mid (170Hz)': 3, 'Mid (350Hz)': 2, 
                    'High Mid (1kHz)': 0, 'Treble (3.5kHz)': 1, 'Presence (8kHz)': 0},
            'Pop': {'Bass (60Hz)': 2, 'Low Mid (170Hz)': 0, 'Mid (350Hz)': 1, 
                   'High Mid (1kHz)': 2, 'Treble (3.5kHz)': 2, 'Presence (8kHz)': 1},
            'Ambient': {'Bass (60Hz)': 1, 'Low Mid (170Hz)': 1, 'Mid (350Hz)': 0, 
                       'High Mid (1kHz)': 0, 'Treble (3.5kHz)': 1, 'Presence (8kHz)': 2}
        }
        
        if self.current_genre in presets:
            preset = presets[self.current_genre]
            for band, value in preset.items():
                if band in self.eq_sliders:
                    self.eq_sliders[band]['slider'].set(value)
                    self.eq_bands[band] = value
                    self.eq_sliders[band]['label'].config(text=f"{value:+.1f}dB")
            
            self.log(f"⚡ Égaliseur ajusté automatiquement pour {self.current_genre}")
            messagebox.showinfo("Ajustement Auto", f"Égaliseur optimisé pour {self.current_genre}")

    def reset_eq(self):
        """Remet l'égaliseur à plat"""
        for band in self.eq_bands:
            self.eq_sliders[band]['slider'].set(0)
            self.eq_bands[band] = 0.0
            self.eq_sliders[band]['label'].config(text="0.0dB")
        
        self.log("🔄 Égaliseur remis à plat")

    def save_settings(self):
        """Sauvegarde les paramètres"""
        settings = {
            'eq_bands': self.eq_bands,
            'current_genre': self.current_genre,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            with open('equalizer_settings.json', 'w') as f:
                json.dump(settings, f, indent=2)
            self.log("💾 Paramètres sauvegardés")
            messagebox.showinfo("Sauvegarde", "Paramètres sauvegardés avec succès")
        except Exception as e:
            self.log(f"❌ Erreur sauvegarde: {e}")

    def log(self, message):
        """Ajoute un message aux logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        def update_log():
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)

    def run(self):
        """Lance l'application"""
        self.log("🚀 AI Equalizer Simplifié démarré")
        if AUDIO_AVAILABLE:
            self.log("✅ Modules audio disponibles")
            self.log("💡 Activez 'Stereo Mix' dans Windows pour capturer l'audio système")
        else:
            self.log("❌ Modules audio manquants")
            self.log("📦 Installez: pip install pyaudio numpy")
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Nettoyage à la fermeture"""
        if self.is_listening:
            self.stop_listening()
        
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()
        
        self.root.destroy()

if __name__ == "__main__":
    app = SimpleAudioEqualizer()
    app.run()
