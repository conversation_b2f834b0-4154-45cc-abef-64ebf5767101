import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/equalizer_provider.dart';
import '../services/equalizer_service.dart';

class EqualizerWidget extends StatelessWidget {
  const EqualizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EqualizerProvider>(
      builder: (context, eqProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // En-tête avec contrôles principaux
              _buildHeader(context, eqProvider),
              const SizedBox(height: 20),
              
              // Gain maître
              _buildMasterGain(context, eqProvider),
              const SizedBox(height: 20),
              
              // Bandes d'égalisation
              Expanded(
                child: _buildEqualizerBands(context, eqProvider),
              ),
              
              const SizedBox(height: 20),
              
              // Presets
              _buildPresets(context, eqProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, EqualizerProvider eqProvider) {
    return Row(
      children: [
        // Activation/Désactivation
        Switch(
          value: eqProvider.isEnabled,
          onChanged: (value) => eqProvider.setEnabled(value),
          activeColor: Colors.blue,
        ),
        const SizedBox(width: 8),
        Text(
          eqProvider.isEnabled ? 'Activé' : 'Désactivé',
          style: TextStyle(
            color: eqProvider.isEnabled ? Colors.green : Colors.grey,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const Spacer(),
        
        // Mode automatique
        Row(
          children: [
            Icon(
              Icons.smart_toy,
              color: eqProvider.autoMode ? Colors.blue : Colors.grey,
              size: 20,
            ),
            const SizedBox(width: 4),
            Switch(
              value: eqProvider.autoMode,
              onChanged: (value) => eqProvider.setAutoMode(value),
              activeColor: Colors.blue,
            ),
            const SizedBox(width: 4),
            const Text('Auto IA'),
          ],
        ),
        
        const SizedBox(width: 16),
        
        // Bouton reset
        ElevatedButton.icon(
          onPressed: () => eqProvider.resetToFlat(),
          icon: const Icon(Icons.refresh, size: 16),
          label: const Text('Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[800],
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildMasterGain(BuildContext context, EqualizerProvider eqProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.volume_up, color: Colors.blue),
              const SizedBox(width: 8),
              const Text(
                'Gain Maître',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                eqProvider.getMasterGainDisplayText(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.blue,
              inactiveTrackColor: Colors.grey[700],
              thumbColor: Colors.blue,
              overlayColor: Colors.blue.withOpacity(0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: eqProvider.masterGain,
              min: -20.0,
              max: 20.0,
              divisions: 80,
              onChanged: (value) => eqProvider.setMasterGain(value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEqualizerBands(BuildContext context, EqualizerProvider eqProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          const Text(
            'Bandes de Fréquences',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Row(
              children: eqProvider.bands.asMap().entries.map((entry) {
                final index = entry.key;
                final band = entry.value;
                return Expanded(
                  child: _buildBandSlider(context, eqProvider, index, band),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBandSlider(BuildContext context, EqualizerProvider eqProvider, int index, EqualizerBand band) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        children: [
          // Nom de la bande
          Text(
            band.name,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Fréquence
          Text(
            '${band.frequency.toInt()}Hz',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[400],
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Slider vertical
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: eqProvider.getBandColor(index),
                  inactiveTrackColor: Colors.grey[700],
                  thumbColor: eqProvider.getBandColor(index),
                  overlayColor: eqProvider.getBandColor(index).withOpacity(0.2),
                  trackHeight: 6,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: band.gain,
                  min: -20.0,
                  max: 20.0,
                  divisions: 80,
                  onChanged: (value) => eqProvider.setBandGain(index, value),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Valeur du gain
          Text(
            eqProvider.getBandGainDisplayText(index),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: eqProvider.getBandColor(index),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPresets(BuildContext context, EqualizerProvider eqProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.library_music, color: Colors.blue),
              const SizedBox(width: 8),
              const Text(
                'Presets',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                'Actuel: ${eqProvider.currentPreset}',
                style: const TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: eqProvider.getAvailablePresets().map((preset) {
              final isSelected = preset == eqProvider.currentPreset;
              return ElevatedButton(
                onPressed: () => eqProvider.applyPreset(preset),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isSelected ? Colors.blue : Colors.grey[800],
                  foregroundColor: isSelected ? Colors.white : Colors.grey[300],
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                child: Text(preset),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
