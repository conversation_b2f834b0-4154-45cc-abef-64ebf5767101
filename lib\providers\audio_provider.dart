import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:math';
import '../services/audio_service.dart';

class AudioProvider extends ChangeNotifier {
  final AudioService _audioService = AudioService();

  StreamSubscription<Float32List>? _audioSubscription;
  StreamSubscription<double>? _volumeSubscription;
  StreamSubscription<Map<String, dynamic>>? _analysisSubscription;

  bool _isProcessing = false;
  double _currentVolume = 0.0;
  double _targetLufs = -23.0;
  double _currentGain = 1.0;
  Map<String, dynamic> _currentAnalysis = {};

  // Paramètres de normalisation
  double _alpha = 0.95;
  double _maxGain = 2.0;
  double _minGain = 0.1;

  // Historique pour visualisation
  final List<double> _volumeHistory = [];
  final List<Map<String, double>> _bandsHistory = [];
  static const int _maxHistoryLength = 100;

  // Getters
  bool get isProcessing => _isProcessing;
  double get currentVolume => _currentVolume;
  double get targetLufs => _targetLufs;
  double get currentGain => _currentGain;
  Map<String, dynamic> get currentAnalysis => Map.from(_currentAnalysis);
  List<double> get volumeHistory => List.from(_volumeHistory);
  List<Map<String, double>> get bandsHistory => List.from(_bandsHistory);

  double get alpha => _alpha;
  double get maxGain => _maxGain;
  double get minGain => _minGain;

  AudioProvider() {
    _setupListeners();
  }

  void _setupListeners() {
    _audioSubscription = _audioService.audioStream.listen(_onAudioData);
    _volumeSubscription = _audioService.volumeStream.listen(_onVolumeData);
    _analysisSubscription = _audioService.analysisStream.listen(_onAnalysisData);
  }

  void _onAudioData(Float32List audioData) {
    // Les données audio sont traitées par le service
    // Ici on peut ajouter des traitements supplémentaires si nécessaire
  }

  void _onVolumeData(double volume) {
    _currentVolume = volume;

    // Mise à jour de l'historique
    _volumeHistory.add(volume);
    if (_volumeHistory.length > _maxHistoryLength) {
      _volumeHistory.removeAt(0);
    }

    notifyListeners();
  }

  void _onAnalysisData(Map<String, dynamic> analysis) {
    _currentAnalysis = analysis;
    _currentGain = analysis['gain']?.toDouble() ?? 1.0;

    // Mise à jour de l'historique des bandes
    if (analysis['bands'] != null) {
      final bands = Map<String, double>.from(analysis['bands']);
      _bandsHistory.add(bands);
      if (_bandsHistory.length > _maxHistoryLength) {
        _bandsHistory.removeAt(0);
      }
    }

    notifyListeners();
  }

  Future<void> startProcessing() async {
    if (_isProcessing) return;

    try {
      await _audioService.startProcessing();
      _isProcessing = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du démarrage du traitement audio: $e');
    }
  }

  void stopProcessing() {
    _audioService.stopProcessing();
    _isProcessing = false;
    notifyListeners();
  }

  void setTargetLufs(double lufs) {
    _targetLufs = lufs.clamp(-60.0, 0.0);
    _audioService.setTargetLufs(_targetLufs);
    notifyListeners();
  }

  void setNormalizationParams({
    double? alpha,
    double? maxGain,
    double? minGain,
  }) {
    if (alpha != null) {
      _alpha = alpha.clamp(0.1, 0.99);
    }
    if (maxGain != null) {
      _maxGain = maxGain.clamp(1.0, 10.0);
    }
    if (minGain != null) {
      _minGain = minGain.clamp(0.01, 1.0);
    }

    _audioService.setNormalizationParams(
      alpha: _alpha,
      maxGain: _maxGain,
      minGain: _minGain,
    );

    notifyListeners();
  }

  // Méthodes utilitaires pour l'interface
  String getVolumeDisplayText() {
    if (_currentVolume == 0) return 'Silence';

    final db = 20 * (log(_currentVolume) / ln10);
    return '${db.toStringAsFixed(1)} dB';
  }

  String getGainDisplayText() {
    final gainDb = 20 * (log(_currentGain) / ln10);
    final sign = gainDb >= 0 ? '+' : '';
    return '$sign${gainDb.toStringAsFixed(1)} dB';
  }

  String getLufsDisplayText() {
    final lufs = _currentAnalysis['lufs']?.toDouble() ?? -100.0;
    return '${lufs.toStringAsFixed(1)} LUFS';
  }

  Color getVolumeColor() {
    if (_currentVolume < 0.1) return Colors.grey;
    if (_currentVolume < 0.5) return Colors.green;
    if (_currentVolume < 0.8) return Colors.orange;
    return Colors.red;
  }

  Color getGainColor() {
    final gainDb = 20 * (log(_currentGain) / ln10);
    if (gainDb.abs() < 1) return Colors.green;
    if (gainDb.abs() < 6) return Colors.orange;
    return Colors.red;
  }

  // Méthodes pour les statistiques
  double getAverageVolume() {
    if (_volumeHistory.isEmpty) return 0.0;
    return _volumeHistory.reduce((a, b) => a + b) / _volumeHistory.length;
  }

  double getPeakVolume() {
    if (_volumeHistory.isEmpty) return 0.0;
    return _volumeHistory.reduce((a, b) => a > b ? a : b);
  }

  Map<String, double> getAverageBands() {
    if (_bandsHistory.isEmpty) return {};

    final averages = <String, double>{};
    final bandNames = _bandsHistory.first.keys;

    for (final bandName in bandNames) {
      double sum = 0.0;
      for (final bands in _bandsHistory) {
        sum += bands[bandName] ?? 0.0;
      }
      averages[bandName] = sum / _bandsHistory.length;
    }

    return averages;
  }

  void clearHistory() {
    _volumeHistory.clear();
    _bandsHistory.clear();
    notifyListeners();
  }

  // Presets de normalisation
  void applyPreset(String presetName) {
    switch (presetName) {
      case 'Soft':
        setNormalizationParams(alpha: 0.98, maxGain: 1.5, minGain: 0.2);
        setTargetLufs(-18.0);
        break;
      case 'Normal':
        setNormalizationParams(alpha: 0.95, maxGain: 2.0, minGain: 0.1);
        setTargetLufs(-23.0);
        break;
      case 'Aggressive':
        setNormalizationParams(alpha: 0.90, maxGain: 3.0, minGain: 0.05);
        setTargetLufs(-16.0);
        break;
      case 'Broadcast':
        setNormalizationParams(alpha: 0.99, maxGain: 1.2, minGain: 0.3);
        setTargetLufs(-23.0);
        break;
    }
  }

  List<String> getAvailablePresets() {
    return ['Soft', 'Normal', 'Aggressive', 'Broadcast'];
  }

  @override
  void dispose() {
    _audioSubscription?.cancel();
    _volumeSubscription?.cancel();
    _analysisSubscription?.cancel();
    _audioService.dispose();
    super.dispose();
  }
}
