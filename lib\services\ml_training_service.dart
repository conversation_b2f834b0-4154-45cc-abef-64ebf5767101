import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

// Service pour entraîner et utiliser des modèles ML personnalisés
class MLTrainingService {
  static const int inputSize = 128; // Taille des features d'entrée
  static const int numGenres = 9; // Nombre de genres à classifier

  // Données d'entraînement simulées
  final List<TrainingExample> _trainingData = [];
  bool _isModelTrained = false;

  // Modèle simple (réseau de neurones basique)
  late List<List<double>> _weights1; // Couche cachée
  late List<double> _bias1;
  late List<List<double>> _weights2; // Couche de sortie
  late List<double> _bias2;

  final List<String> _genreLabels = [
    'Rock', 'Pop', 'Classical', 'Jazz', 'Electronic',
    'Hip-Hop', 'Country', 'Blues', 'Reggae'
  ];

  MLTrainingService() {
    _initializeModel();
  }

  void _initializeModel() {
    final random = Random();

    // Initialisation des poids (couche cachée: 128 -> 64)
    _weights1 = List.generate(64, (_) =>
      List.generate(inputSize, (_) => (random.nextDouble() - 0.5) * 0.1)
    );
    _bias1 = List.generate(64, (_) => (random.nextDouble() - 0.5) * 0.1);

    // Couche de sortie: 64 -> 9 genres
    _weights2 = List.generate(numGenres, (_) =>
      List.generate(64, (_) => (random.nextDouble() - 0.5) * 0.1)
    );
    _bias2 = List.generate(numGenres, (_) => (random.nextDouble() - 0.5) * 0.1);
  }

  // Génération de données d'entraînement synthétiques
  Future<void> generateTrainingData() async {
    debugPrint('Generating synthetic training data...');

    for (int genreIndex = 0; genreIndex < _genreLabels.length; genreIndex++) {
      final genre = _genreLabels[genreIndex];

      // Générer 100 exemples par genre
      for (int i = 0; i < 100; i++) {
        final features = _generateGenreFeatures(genre);
        final label = List.filled(numGenres, 0.0);
        label[genreIndex] = 1.0; // One-hot encoding

        _trainingData.add(TrainingExample(features, label, genre));
      }
    }

    // Mélanger les données
    _trainingData.shuffle();
    debugPrint('Generated ${_trainingData.length} training examples');
  }

  // Génération de features caractéristiques pour chaque genre
  List<double> _generateGenreFeatures(String genre) {
    final random = Random();
    final features = List<double>.filled(inputSize, 0.0);

    // Features de base (0-19: caractéristiques spectrales)
    switch (genre) {
      case 'Rock':
        // Rock: beaucoup de basses et d'aigus, médiums creusés
        for (int i = 0; i < 5; i++) features[i] = 0.7 + random.nextDouble() * 0.3; // Basses
        for (int i = 5; i < 10; i++) features[i] = 0.3 + random.nextDouble() * 0.4; // Médiums
        for (int i = 10; i < 15; i++) features[i] = 0.6 + random.nextDouble() * 0.4; // Aigus
        features[15] = 120 + random.nextDouble() * 40; // Tempo
        features[16] = 0.6 + random.nextDouble() * 0.3; // Dynamique
        break;

      case 'Classical':
        // Classical: équilibré, très dynamique
        for (int i = 0; i < 15; i++) features[i] = 0.4 + random.nextDouble() * 0.3;
        features[15] = 60 + random.nextDouble() * 60; // Tempo variable
        features[16] = 0.8 + random.nextDouble() * 0.2; // Très dynamique
        features[17] = 0.8 + random.nextDouble() * 0.2; // Harmonique
        break;

      case 'Electronic':
        // Electronic: basses très présentes, aigus synthétiques
        for (int i = 0; i < 3; i++) features[i] = 0.8 + random.nextDouble() * 0.2; // Sub-bass
        for (int i = 12; i < 15; i++) features[i] = 0.7 + random.nextDouble() * 0.3; // Aigus
        features[15] = 120 + random.nextDouble() * 60; // Tempo élevé
        features[18] = 0.3 + random.nextDouble() * 0.4; // Artificiel
        break;

      case 'Jazz':
        // Jazz: médiums présents, complexité harmonique
        for (int i = 3; i < 12; i++) features[i] = 0.5 + random.nextDouble() * 0.3;
        features[15] = 80 + random.nextDouble() * 60; // Tempo modéré
        features[17] = 0.7 + random.nextDouble() * 0.3; // Harmonique
        features[19] = 0.6 + random.nextDouble() * 0.4; // Complexité
        break;

      default:
        // Autres genres: features aléatoires avec tendances
        for (int i = 0; i < 20; i++) {
          features[i] = random.nextDouble();
        }
    }

    // Features rythmiques (20-39)
    final rhythmBase = _getGenreRhythmBase(genre);
    for (int i = 20; i < 40; i++) {
      features[i] = rhythmBase + (random.nextDouble() - 0.5) * 0.2;
    }

    // Features harmoniques (40-79)
    final harmonicBase = _getGenreHarmonicBase(genre);
    for (int i = 40; i < 80; i++) {
      features[i] = harmonicBase + (random.nextDouble() - 0.5) * 0.3;
    }

    // Features temporelles (80-127)
    for (int i = 80; i < inputSize; i++) {
      features[i] = random.nextDouble();
    }

    return features;
  }

  double _getGenreRhythmBase(String genre) {
    switch (genre) {
      case 'Rock': return 0.7;
      case 'Electronic': return 0.8;
      case 'Hip-Hop': return 0.9;
      case 'Reggae': return 0.6;
      case 'Classical': return 0.3;
      default: return 0.5;
    }
  }

  double _getGenreHarmonicBase(String genre) {
    switch (genre) {
      case 'Classical': return 0.8;
      case 'Jazz': return 0.7;
      case 'Blues': return 0.6;
      case 'Electronic': return 0.3;
      default: return 0.5;
    }
  }

  // Entraînement du modèle
  Future<void> trainModel({int epochs = 100, double learningRate = 0.01}) async {
    if (_trainingData.isEmpty) {
      await generateTrainingData();
    }

    debugPrint('Training model with ${_trainingData.length} examples...');

    for (int epoch = 0; epoch < epochs; epoch++) {
      double totalLoss = 0.0;

      for (final example in _trainingData) {
        // Forward pass
        final prediction = _predict(example.features);

        // Calcul de la loss
        final loss = _calculateLoss(prediction, example.label);
        totalLoss += loss;

        // Backward pass (gradient descent simplifié)
        _updateWeights(example.features, prediction, example.label, learningRate);
      }

      if (epoch % 10 == 0) {
        final avgLoss = totalLoss / _trainingData.length;
        debugPrint('Epoch $epoch: Average Loss = ${avgLoss.toStringAsFixed(4)}');
      }
    }

    _isModelTrained = true;
    debugPrint('Model training completed!');
  }

  // Prédiction
  List<double> _predict(List<double> features) {
    // Couche cachée
    final hidden = List<double>.filled(64, 0.0);
    for (int i = 0; i < 64; i++) {
      double sum = _bias1[i];
      for (int j = 0; j < inputSize; j++) {
        sum += features[j] * _weights1[i][j];
      }
      hidden[i] = _relu(sum);
    }

    // Couche de sortie
    final output = List<double>.filled(numGenres, 0.0);
    for (int i = 0; i < numGenres; i++) {
      double sum = _bias2[i];
      for (int j = 0; j < 64; j++) {
        sum += hidden[j] * _weights2[i][j];
      }
      output[i] = sum;
    }

    // Softmax
    return _softmax(output);
  }

  double _relu(double x) => x > 0 ? x : 0;

  List<double> _softmax(List<double> x) {
    final maxVal = x.reduce(max);
    final expValues = x.map((val) => exp(val - maxVal)).toList();
    final sum = expValues.reduce((a, b) => a + b);
    return expValues.map((val) => val / sum).toList();
  }

  double _calculateLoss(List<double> prediction, List<double> target) {
    double loss = 0.0;
    for (int i = 0; i < prediction.length; i++) {
      loss -= target[i] * log(prediction[i] + 1e-15); // Cross-entropy
    }
    return loss;
  }

  void _updateWeights(List<double> features, List<double> prediction,
                     List<double> target, double learningRate) {
    // Gradient descent simplifié
    // Dans une vraie implémentation, on calculerait les gradients complets

    for (int i = 0; i < numGenres; i++) {
      final error = prediction[i] - target[i];
      _bias2[i] -= learningRate * error;

      for (int j = 0; j < 64; j++) {
        _weights2[i][j] -= learningRate * error * 0.1; // Approximation
      }
    }
  }

  // Classification d'un échantillon audio
  Map<String, double> classifyAudio(List<double> audioFeatures) {
    if (!_isModelTrained) {
      throw Exception('Model not trained yet. Call trainModel() first.');
    }

    final prediction = _predict(audioFeatures);
    final result = <String, double>{};

    for (int i = 0; i < _genreLabels.length; i++) {
      result[_genreLabels[i]] = prediction[i];
    }

    return result;
  }

  // Sauvegarde du modèle
  Future<void> saveModel(String path) async {
    final modelData = {
      'weights1': _weights1,
      'bias1': _bias1,
      'weights2': _weights2,
      'bias2': _bias2,
      'trained': _isModelTrained,
    };

    // Dans une vraie implémentation, on sauvegarderait en format TensorFlow Lite
    debugPrint('Model saved to $path (simulation)');
  }

  // Chargement du modèle
  Future<void> loadModel(String path) async {
    // Dans une vraie implémentation, on chargerait depuis un fichier .tflite
    debugPrint('Model loaded from $path (simulation)');
    _isModelTrained = true;
  }

  // Évaluation du modèle
  Future<Map<String, double>> evaluateModel() async {
    if (!_isModelTrained) {
      throw Exception('Model not trained yet.');
    }

    int correct = 0;
    int total = 0;

    for (final example in _trainingData) {
      final prediction = _predict(example.features);
      final predictedGenre = _genreLabels[_argmax(prediction)];

      if (predictedGenre == example.genre) {
        correct++;
      }
      total++;
    }

    final accuracy = correct / total;

    return {
      'accuracy': accuracy,
      'correct': correct.toDouble(),
      'total': total.toDouble(),
    };
  }

  int _argmax(List<double> list) {
    int maxIndex = 0;
    for (int i = 1; i < list.length; i++) {
      if (list[i] > list[maxIndex]) {
        maxIndex = i;
      }
    }
    return maxIndex;
  }

  // Amélioration continue avec de nouveaux exemples
  void addTrainingExample(List<double> features, String genre) {
    final label = List.filled(numGenres, 0.0);
    final genreIndex = _genreLabels.indexOf(genre);
    if (genreIndex != -1) {
      label[genreIndex] = 1.0;
      _trainingData.add(TrainingExample(features, label, genre));
    }
  }

  // Réentraînement incrémental
  Future<void> incrementalTraining({int epochs = 10}) async {
    if (_trainingData.length < 50) return;

    // Prendre seulement les derniers exemples pour l'entraînement incrémental
    final recentData = _trainingData.length > 100
        ? _trainingData.sublist(_trainingData.length - 100)
        : _trainingData;

    await trainModel(epochs: epochs, learningRate: 0.001); // Learning rate plus faible
  }

  bool get isModelTrained => _isModelTrained;
  List<String> get genreLabels => List.from(_genreLabels);
}

class TrainingExample {
  final List<double> features;
  final List<double> label;
  final String genre;

  TrainingExample(this.features, this.label, this.genre);
}
