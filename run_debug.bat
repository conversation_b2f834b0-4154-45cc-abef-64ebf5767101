@echo off
echo Starting AI Equalizer in Debug Mode...

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    echo Please install Flutter from https://flutter.dev/docs/get-started/install/windows
    pause
    exit /b 1
)

REM Get dependencies if needed
if not exist "pubspec.lock" (
    echo Getting dependencies...
    flutter pub get
)

REM Run in debug mode
echo Running AI Equalizer in debug mode...
echo Press Ctrl+C to stop the application
echo.
flutter run -d windows

pause
