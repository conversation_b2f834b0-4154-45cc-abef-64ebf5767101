@echo off
echo ========================================
echo    AI EQUALIZER SIMPLIFIE - INSTALL
echo ========================================
echo.

echo [1/3] Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe
    echo Telechargez Python depuis: https://python.org
    pause
    exit /b 1
)
echo ✅ Python detecte

echo.
echo [2/3] Installation des modules essentiels...
echo Installation de pyaudio et numpy seulement...

pip install pyaudio numpy

if errorlevel 1 (
    echo.
    echo ⚠️ Erreur avec pip, tentative alternative...
    python -m pip install --user pyaudio numpy
)

echo ✅ Modules installes

echo.
echo [3/3] Configuration audio Windows...
echo.
echo 🔊 IMPORTANT: Pour capturer l'audio systeme:
echo.
echo 1. Clic droit sur l'icone son (barre des taches)
echo 2. "Ouvrir les parametres de son"
echo 3. "Panneau de configuration du son"
echo 4. Onglet "Enregistrement"
echo 5. Clic droit dans la zone vide
echo 6. "Afficher les peripheriques desactives"
echo 7. Clic droit sur "Stereo Mix" ou "Mixage stereo"
echo 8. "Activer"
echo.
echo ⚠️ SANS CETTE ETAPE, L'APPLICATION UTILISERA LE MICROPHONE
echo.
echo Appuyez sur une touche quand c'est fait...
pause

echo.
echo 🎵 Lancement de l'AI Equalizer Simplifie...
echo.

python simple_audio_equalizer.py

echo.
echo Application fermee.
pause
