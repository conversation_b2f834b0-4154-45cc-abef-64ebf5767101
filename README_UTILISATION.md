# 🎵 AI Equalizer - Capture Audio Système RÉELLE

## 🚀 INSTALLATION RAPIDE

### Option 1: Installation Automatique (Recommandée)
1. **Double-cliquez sur `install_and_run.bat`**
2. **Suivez les instructions à l'écran**
3. **Activez "Stereo Mix" quand demandé**
4. **L'application se lance automatiquement**

### Option 2: Installation Manuelle
```bash
# Installer les dépendances
pip install pyaudio librosa scipy matplotlib pycaw numpy comtypes

# Lancer l'application
python real_audio_equalizer.py
```

## ⚙️ CONFIGURATION WINDOWS (CRUCIAL)

### Activer "Stereo Mix" pour capturer l'audio système :

1. **Clic droit sur l'icône son** (barre des tâches)
2. **"Ouvrir les paramètres de son"**
3. **"Panneau de configuration du son"**
4. **Onglet "Enregistrement"**
5. **Clic droit dans la zone vide**
6. **"Afficher les périphériques désactivés"**
7. **Clic droit sur "Stereo Mix" ou "Mixage stéréo"**
8. **"Activer"**

⚠️ **SANS CETTE ÉTAPE, L'APPLICATION NE PEUT PAS CAPTURER L'AUDIO SYSTÈME !**

## 🎮 UTILISATION

### 1. Démarrage
- Lancez l'application
- Cliquez **"🎧 Démarrer l'Écoute"**
- Lancez votre musique YouTube/Spotify/etc.

### 2. Fonctionnalités

#### 🤖 Détection IA Automatique
- **Genres détectés** : Rock, Pop, Electronic, Classical, Jazz
- **Confiance** : Pourcentage de certitude
- **Temps réel** : Analyse continue pendant la lecture

#### 🎛️ Égaliseur 6 Bandes
- **Bass (60Hz)** : Graves profondes
- **Low Mid (170Hz)** : Bas-médiums
- **Mid (350Hz)** : Médiums
- **High Mid (1kHz)** : Hauts-médiums
- **Treble (3.5kHz)** : Aigus
- **Presence (8kHz)** : Présence

#### ⚡ Ajustement Automatique
- Cliquez **"⚡ Ajustement Auto"**
- L'égaliseur s'optimise selon le genre détecté
- Presets professionnels intégrés

#### 📊 Visualisation Temps Réel
- **Signal audio** : Forme d'onde en direct
- **Spectre fréquentiel** : Analyse des fréquences
- **Logs détaillés** : Historique des actions

### 3. Contrôles

#### Boutons Principaux
- **🎧 Démarrer/Arrêter** : Active/désactive la capture
- **⚡ Ajustement Auto** : Optimisation automatique
- **🔄 Reset** : Remet l'égaliseur à plat
- **💾 Sauver** : Sauvegarde les paramètres

#### Sliders d'Égalisation
- **Range** : -20dB à +20dB
- **Précision** : 0.5dB
- **Temps réel** : Effet immédiat

## 🎯 TEST PRATIQUE

### Étapes de Test
1. **Lancez l'application**
2. **Activez l'écoute**
3. **Ouvrez YouTube** et lancez différents genres :
   - **Rock** → Devrait détecter "Rock" et booster basses/aigus
   - **Musique électronique** → "Electronic" avec sub-bass
   - **Classique** → "Classical" avec dynamique
   - **Jazz** → "Jazz" avec médiums
   - **Pop** → "Pop" équilibré

4. **Testez l'ajustement auto** pour chaque genre
5. **Modifiez manuellement** les sliders
6. **Observez les graphiques** en temps réel

### Résultats Attendus
- ✅ **Détection de genre** change selon la musique
- ✅ **Graphiques** bougent avec l'audio
- ✅ **Logs** montrent l'activité en temps réel
- ✅ **Ajustement auto** modifie l'égaliseur
- ✅ **Sliders manuels** fonctionnent

## 🔧 DÉPANNAGE

### Problème : "Modules audio non disponibles"
**Solution** :
```bash
pip install --upgrade pip
pip install pyaudio librosa scipy matplotlib pycaw
```

### Problème : "Aucun audio détecté"
**Solutions** :
1. Vérifiez que **Stereo Mix est activé**
2. Augmentez le volume de votre musique
3. Vérifiez que l'audio joue bien sur Windows

### Problème : "Erreur PyAudio"
**Solution** :
```bash
# Windows
pip install pipwin
pipwin install pyaudio

# Ou télécharger le wheel depuis :
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
```

### Problème : "Permission denied"
**Solution** :
- Lancez l'invite de commande **en tant qu'administrateur**
- Ou utilisez `pip install --user`

## 📋 SPÉCIFICATIONS TECHNIQUES

### Configuration Requise
- **OS** : Windows 10/11
- **Python** : 3.7+
- **RAM** : 4GB minimum
- **Audio** : Carte son avec Stereo Mix

### Modules Python
- **pyaudio** : Capture audio
- **librosa** : Analyse musicale
- **scipy** : Traitement signal
- **matplotlib** : Visualisation
- **pycaw** : Contrôle volume Windows
- **numpy** : Calculs numériques
- **tkinter** : Interface graphique

### Performances
- **Latence** : <50ms
- **Fréquence d'échantillonnage** : 44.1kHz
- **Taille buffer** : 1024 échantillons
- **Analyse temps réel** : Oui

## 🎵 CETTE VERSION FONCTIONNE VRAIMENT !

Contrairement aux versions précédentes qui simulaient, cette application :

✅ **Capture VRAIMENT l'audio système Windows**  
✅ **Analyse les fréquences en temps réel**  
✅ **Détecte les genres musicaux par IA**  
✅ **Affiche des graphiques temps réel**  
✅ **Applique des égaliseurs professionnels**  
✅ **Fonctionne avec YouTube, Spotify, etc.**  

## 🚀 LANCEZ MAINTENANT !

**Double-cliquez sur `install_and_run.bat` et testez avec votre musique !**
