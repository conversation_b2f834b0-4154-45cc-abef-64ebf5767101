@echo off
echo Building AI Equalizer for Windows...

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    echo Please install Flutter from https://flutter.dev/docs/get-started/install/windows
    pause
    exit /b 1
)

REM Clean previous builds
echo Cleaning previous builds...
flutter clean

REM Get dependencies
echo Getting dependencies...
flutter pub get

REM Build for Windows
echo Building for Windows...
flutter build windows --release

REM Check if build was successful
if %errorlevel% equ 0 (
    echo.
    echo Build successful!
    echo Executable location: build\windows\runner\Release\ai_equalizer.exe
    echo.
    echo You can now run the application by executing:
    echo build\windows\runner\Release\ai_equalizer.exe
    echo.
) else (
    echo.
    echo Build failed!
    echo Please check the error messages above.
    echo.
)

pause
