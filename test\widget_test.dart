import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:ai_equalizer/main.dart';
import 'package:ai_equalizer/providers/audio_provider.dart';
import 'package:ai_equalizer/providers/equalizer_provider.dart';
import 'package:ai_equalizer/services/audio_service.dart';
import 'package:ai_equalizer/services/ai_service.dart';
import 'package:ai_equalizer/services/equalizer_service.dart';

void main() {
  group('AI Equalizer Tests', () {
    testWidgets('App should start without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AudioProvider()),
            ChangeNotifierProvider(create: (_) => EqualizerProvider()),
            Provider(create: (_) => AudioService()),
            Provider(create: (_) => AIService()),
            Provider(create: (_) => EqualizerService()),
          ],
          child: MaterialApp(
            title: 'AI Equalizer Test',
            theme: ThemeData(
              primarySwatch: Colors.blue,
              brightness: Brightness.dark,
            ),
            home: const Scaffold(
              body: Center(
                child: Text('AI Equalizer Test'),
              ),
            ),
          ),
        ),
      );

      // Verify that the app starts
      expect(find.text('AI Equalizer Test'), findsOneWidget);
    });

    testWidgets('Audio service should initialize', (WidgetTester tester) async {
      final audioService = AudioService();
      expect(audioService.isProcessing, false);
      expect(audioService.targetLufs, -23.0);
      expect(audioService.currentGain, 1.0);
    });

    testWidgets('Equalizer service should have correct bands', (WidgetTester tester) async {
      final equalizerService = EqualizerService();
      expect(equalizerService.bands.length, 6);
      expect(equalizerService.isEnabled, true);
      expect(equalizerService.masterGain, 0.0);
    });

    testWidgets('AI service should initialize with unknown genre', (WidgetTester tester) async {
      final aiService = AIService();
      expect(aiService.currentGenre, MusicGenre.unknown);
      expect(aiService.isAnalyzing, false);
    });
  });

  group('Audio Processing Tests', () {
    test('RMS calculation should work correctly', () {
      // Test RMS calculation with known values
      final audioService = AudioService();
      // Note: This would require exposing internal methods for testing
      // In a real implementation, we'd create testable interfaces
    });

    test('LUFS conversion should be accurate', () {
      // Test LUFS calculation
      // This would test the mathematical formulas
    });

    test('Gain limiting should prevent clipping', () {
      // Test that gain is properly limited
      final audioService = AudioService();
      audioService.setNormalizationParams(maxGain: 2.0, minGain: 0.1);
      // Test that gains are within limits
    });
  });

  group('Equalizer Tests', () {
    test('Band gain should be limited to ±20dB', () {
      final equalizerService = EqualizerService();
      
      // Test setting gain within limits
      equalizerService.setBandGain(0, 10.0);
      expect(equalizerService.bands[0].gain, 10.0);
      
      // Test setting gain beyond limits (should be clamped)
      equalizerService.setBandGain(0, 25.0);
      expect(equalizerService.bands[0].gain, 20.0);
      
      equalizerService.setBandGain(0, -25.0);
      expect(equalizerService.bands[0].gain, -20.0);
    });

    test('Presets should apply correctly', () {
      final equalizerService = EqualizerService();
      
      // Test applying a preset
      equalizerService.applyPreset('Rock');
      expect(equalizerService.currentPreset, 'Rock');
      
      // Test that gains are applied
      final rockPreset = equalizerService.bands;
      expect(rockPreset.any((band) => band.gain != 0.0), true);
    });

    test('Reset should clear all gains', () {
      final equalizerService = EqualizerService();
      
      // Set some gains
      equalizerService.setBandGain(0, 5.0);
      equalizerService.setBandGain(1, -3.0);
      equalizerService.setMasterGain(2.0);
      
      // Reset
      equalizerService.resetToFlat();
      
      // Check all gains are zero
      for (final band in equalizerService.bands) {
        expect(band.gain, 0.0);
      }
      expect(equalizerService.masterGain, 0.0);
    });
  });

  group('AI Classification Tests', () {
    test('Genre classification should return valid genres', () {
      final aiService = AIService();
      
      // Test that all genre values are valid
      for (final genre in MusicGenre.values) {
        final genreName = aiService.getGenreName(genre);
        expect(genreName.isNotEmpty, true);
      }
    });

    test('Equalizer presets should exist for all genres', () {
      final aiService = AIService();
      
      // Test that presets exist for all genres
      for (final genre in MusicGenre.values) {
        final preset = aiService.getEqualizerPreset(genre);
        expect(preset.isNotEmpty, true);
        
        // Check that preset has expected keys
        expect(preset.containsKey('bass'), true);
        expect(preset.containsKey('treble'), true);
      }
    });
  });

  group('Provider Tests', () {
    testWidgets('AudioProvider should manage state correctly', (WidgetTester tester) async {
      final audioProvider = AudioProvider();
      
      expect(audioProvider.isProcessing, false);
      expect(audioProvider.targetLufs, -23.0);
      
      // Test setting target LUFS
      audioProvider.setTargetLufs(-18.0);
      expect(audioProvider.targetLufs, -18.0);
      
      // Test that values are clamped
      audioProvider.setTargetLufs(-100.0);
      expect(audioProvider.targetLufs, -60.0);
      
      audioProvider.setTargetLufs(10.0);
      expect(audioProvider.targetLufs, 0.0);
    });

    testWidgets('EqualizerProvider should manage equalizer state', (WidgetTester tester) async {
      final eqProvider = EqualizerProvider();
      
      expect(eqProvider.isEnabled, true);
      expect(eqProvider.autoMode, false);
      expect(eqProvider.currentPreset, 'Flat');
      
      // Test enabling auto mode
      eqProvider.setAutoMode(true);
      expect(eqProvider.autoMode, true);
      
      // Test applying preset
      eqProvider.applyPreset('Rock');
      expect(eqProvider.currentPreset, 'Rock');
    });
  });
}
