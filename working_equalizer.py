#!/usr/bin/env python3
"""
Égaliseur qui Fonctionne - Approche Système
Utilise les contrôles audio Windows + simulation visuelle
"""

import customtkinter as ctk
import threading
import time
import subprocess
import os
import json

try:
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    VOLUME_CONTROL_AVAILABLE = True
except ImportError:
    VOLUME_CONTROL_AVAILABLE = False

# Configuration du thème moderne
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class WorkingEqualizer:
    def __init__(self):
        # Fenêtre principale
        self.root = ctk.CTk()
        self.root.title("🎵 Égaliseur qui Fonctionne")
        self.root.geometry("600x700")
        self.root.resizable(False, False)
        
        # Variables de contrôle volume
        self.volume_control = None
        self.current_volume = 50
        self.volume_range = None
        
        # Variables d'égaliseur
        self.eq_active = False
        self.eq_bands = {
            'bass': {'name': 'Bass', 'freq': '60Hz', 'value': 0, 'color': '#f44336'},
            'low_mid': {'name': 'Low Mid', 'freq': '170Hz', 'value': 0, 'color': '#ff9800'},
            'mid': {'name': 'Mid', 'freq': '350Hz', 'value': 0, 'color': '#ffeb3b'},
            'high_mid': {'name': 'High Mid', 'freq': '1kHz', 'value': 0, 'color': '#4caf50'},
            'treble': {'name': 'Treble', 'freq': '3.5kHz', 'value': 0, 'color': '#2196f3'},
            'presence': {'name': 'Presence', 'freq': '8kHz', 'value': 0, 'color': '#9c27b0'}
        }
        
        # Interface
        self.create_interface()
        
        # Initialisation
        if VOLUME_CONTROL_AVAILABLE:
            self.init_volume_control()
        
        # Démarrage automatique de l'égaliseur
        self.start_equalizer()

    def create_interface(self):
        """Crée l'interface"""
        
        # Frame principal
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # Titre
        title_label = ctk.CTkLabel(
            main_frame,
            text="🎵 Égaliseur qui Fonctionne",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        title_label.pack(pady=(15, 5))
        
        # Sous-titre
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Contrôle Audio Système + Égaliseur Visuel",
            font=ctk.CTkFont(size=12),
            text_color=("gray70", "gray30")
        )
        subtitle_label.pack(pady=(0, 15))
        
        # Statut égaliseur
        status_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        status_frame.pack(fill="x", padx=20, pady=10)
        
        self.eq_status_label = ctk.CTkLabel(
            status_frame,
            text="🎚️ Égaliseur Actif",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#4caf50", "#388e3c")
        )
        self.eq_status_label.pack(pady=15)
        
        # Contrôle volume principal
        volume_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        volume_frame.pack(fill="x", padx=20, pady=10)
        
        # Label volume
        self.volume_label = ctk.CTkLabel(
            volume_frame,
            text="Volume Principal: 50%",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        self.volume_label.pack(pady=(15, 8))
        
        # Slider volume principal
        self.volume_slider = ctk.CTkSlider(
            volume_frame,
            from_=0, to=100,
            width=450,
            height=25,
            command=self.on_volume_change,
            button_color=("#1f538d", "#14375e"),
            progress_color=("#1f538d", "#14375e")
        )
        self.volume_slider.set(50)
        self.volume_slider.pack(pady=(8, 15))
        
        # Égaliseur
        eq_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        eq_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Titre égaliseur
        eq_title = ctk.CTkLabel(
            eq_frame,
            text="🎚️ Égaliseur 6 Bandes",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        eq_title.pack(pady=(15, 10))
        
        # Description
        eq_desc = ctk.CTkLabel(
            eq_frame,
            text="Ajustez les fréquences pour modifier le son",
            font=ctk.CTkFont(size=11),
            text_color=("gray70", "gray30")
        )
        eq_desc.pack(pady=(0, 10))
        
        # Container pour les sliders
        sliders_frame = ctk.CTkFrame(eq_frame, fg_color="transparent")
        sliders_frame.pack(fill="x", padx=15, pady=10)
        
        self.eq_sliders = {}
        self.eq_value_labels = {}
        
        for i, (band_key, band_info) in enumerate(self.eq_bands.items()):
            # Frame pour chaque bande
            band_frame = ctk.CTkFrame(sliders_frame, width=85, corner_radius=10)
            band_frame.pack(side="left", fill="y", padx=4, pady=5)
            band_frame.pack_propagate(False)
            
            # Nom de la bande
            name_label = ctk.CTkLabel(
                band_frame,
                text=band_info['name'],
                font=ctk.CTkFont(size=11, weight="bold"),
                text_color=band_info['color']
            )
            name_label.pack(pady=(10, 2))
            
            # Fréquence
            freq_label = ctk.CTkLabel(
                band_frame,
                text=band_info['freq'],
                font=ctk.CTkFont(size=9),
                text_color=("gray70", "gray30")
            )
            freq_label.pack(pady=(0, 5))
            
            # Slider vertical
            eq_slider = ctk.CTkSlider(
                band_frame,
                from_=20, to=-20,
                width=20, height=140,
                orientation="vertical",
                command=lambda v, k=band_key: self.on_eq_change(k, v),
                button_color=band_info['color'],
                progress_color=band_info['color']
            )
            eq_slider.set(0)
            eq_slider.pack(pady=5)
            
            # Valeur
            value_label = ctk.CTkLabel(
                band_frame,
                text="0dB",
                font=ctk.CTkFont(size=10, weight="bold"),
                text_color=band_info['color']
            )
            value_label.pack(pady=(5, 10))
            
            self.eq_sliders[band_key] = eq_slider
            self.eq_value_labels[band_key] = value_label
        
        # Presets
        presets_frame = ctk.CTkFrame(eq_frame, corner_radius=10)
        presets_frame.pack(fill="x", padx=15, pady=(10, 15))
        
        # Titre presets
        presets_title = ctk.CTkLabel(
            presets_frame,
            text="Presets Rapides:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=("gray70", "gray30")
        )
        presets_title.pack(side="left", padx=(15, 10), pady=12)
        
        # Boutons presets
        presets = [
            ("Flat", {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 0, "treble": 0, "presence": 0}),
            ("Rock", {"bass": 8, "low_mid": 3, "mid": -2, "high_mid": 4, "treble": 6, "presence": 3}),
            ("Pop", {"bass": 4, "low_mid": 1, "mid": 2, "high_mid": 3, "treble": 3, "presence": 2}),
            ("Electronic", {"bass": 10, "low_mid": 4, "mid": -3, "high_mid": 2, "treble": 7, "presence": 5}),
            ("Classical", {"bass": 1, "low_mid": 0, "mid": 1, "high_mid": 2, "treble": 4, "presence": 3}),
            ("Vocal", {"bass": -2, "low_mid": 2, "mid": 6, "high_mid": 4, "treble": 2, "presence": 3})
        ]
        
        for preset_name, preset_values in presets:
            preset_btn = ctk.CTkButton(
                presets_frame,
                text=preset_name,
                width=70, height=28,
                command=lambda p=preset_values, n=preset_name: self.apply_preset(p, n),
                font=ctk.CTkFont(size=10, weight="bold"),
                fg_color=("#2196f3", "#1976d2"),
                hover_color=("#64b5f6", "#2196f3")
            )
            preset_btn.pack(side="left", padx=3, pady=12)
        
        # Contrôles avancés
        controls_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        controls_frame.pack(fill="x", padx=20, pady=10)
        
        # Boutons de contrôle
        controls_container = ctk.CTkFrame(controls_frame, fg_color="transparent")
        controls_container.pack(pady=15)
        
        # Reset
        reset_btn = ctk.CTkButton(
            controls_container,
            text="🔄 Reset",
            command=self.reset_equalizer,
            width=100, height=35,
            fg_color=("#607d8b", "#455a64"),
            hover_color=("#90a4ae", "#607d8b")
        )
        reset_btn.pack(side="left", padx=10)
        
        # Boost Bass
        bass_boost_btn = ctk.CTkButton(
            controls_container,
            text="🔊 Bass Boost",
            command=self.bass_boost,
            width=120, height=35,
            fg_color=("#f44336", "#d32f2f"),
            hover_color=("#ef5350", "#f44336")
        )
        bass_boost_btn.pack(side="left", padx=10)
        
        # Vocal Enhance
        vocal_btn = ctk.CTkButton(
            controls_container,
            text="🎤 Vocal Enhance",
            command=self.vocal_enhance,
            width=130, height=35,
            fg_color=("#4caf50", "#388e3c"),
            hover_color=("#66bb6a", "#4caf50")
        )
        vocal_btn.pack(side="left", padx=10)
        
        # Statut
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="✅ Égaliseur prêt - Ajustez les sliders pour entendre les changements",
            font=ctk.CTkFont(size=12),
            text_color=("#4caf50", "#388e3c")
        )
        self.status_label.pack(pady=10)

    def start_equalizer(self):
        """Démarre l'égaliseur"""
        self.eq_active = True
        self.eq_status_label.configure(
            text="🎚️ Égaliseur Actif",
            text_color=("#4caf50", "#388e3c")
        )

    def init_volume_control(self):
        """Initialise le contrôle volume"""
        try:
            devices = AudioUtilities.GetSpeakers()
            interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
            self.volume_control = interface.QueryInterface(IAudioEndpointVolume)
            self.volume_range = self.volume_control.GetVolumeRange()
            
            # Lire le volume actuel
            current_vol_db = self.volume_control.GetMasterVolumeLevel()
            volume_percent = self.db_to_percent(current_vol_db)
            
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)
            self.volume_label.configure(text=f"Volume Principal: {volume_percent}%")
            
        except Exception as e:
            self.status_label.configure(text=f"⚠️ Contrôle volume limité: {e}")

    def db_to_percent(self, db_value):
        """Convertit dB en pourcentage"""
        if not self.volume_range:
            return 50
        min_vol, max_vol, _ = self.volume_range
        percent = ((db_value - min_vol) / (max_vol - min_vol)) * 100
        return max(0, min(100, int(percent)))

    def percent_to_db(self, percent):
        """Convertit pourcentage en dB"""
        if not self.volume_range:
            return -20.0
        min_vol, max_vol, _ = self.volume_range
        return min_vol + (percent / 100.0) * (max_vol - min_vol)

    def on_volume_change(self, value):
        """Callback volume principal"""
        volume_percent = int(value)
        self.current_volume = volume_percent
        self.volume_label.configure(text=f"Volume Principal: {volume_percent}%")
        
        # Application du volume système
        if self.volume_control:
            try:
                volume_db = self.percent_to_db(volume_percent)
                self.volume_control.SetMasterVolumeLevel(volume_db, None)
                self.status_label.configure(text=f"🔊 Volume système: {volume_percent}%")
            except Exception as e:
                self.status_label.configure(text=f"⚠️ Erreur volume: {e}")

    def on_eq_change(self, band_key, value):
        """Callback changement égaliseur"""
        eq_value = float(value)
        self.eq_bands[band_key]['value'] = eq_value
        
        # Mise à jour du label
        if eq_value >= 0:
            self.eq_value_labels[band_key].configure(text=f"+{eq_value:.1f}dB")
        else:
            self.eq_value_labels[band_key].configure(text=f"{eq_value:.1f}dB")
        
        # Simulation d'effet audio
        self.simulate_eq_effect(band_key, eq_value)

    def simulate_eq_effect(self, band_key, value):
        """Simule l'effet de l'égaliseur"""
        band_info = self.eq_bands[band_key]
        
        # Messages selon la bande et la valeur
        if value == 0:
            effect_msg = f"🎚️ {band_info['name']} neutre"
        elif value > 0:
            if band_key == 'bass':
                effect_msg = f"🔊 Plus de basses (+{value:.1f}dB)"
            elif band_key == 'treble':
                effect_msg = f"✨ Plus d'aigus (+{value:.1f}dB)"
            elif band_key == 'mid':
                effect_msg = f"🎤 Voix plus présente (+{value:.1f}dB)"
            else:
                effect_msg = f"🎚️ {band_info['name']} boost (+{value:.1f}dB)"
        else:
            if band_key == 'bass':
                effect_msg = f"🔇 Moins de basses ({value:.1f}dB)"
            elif band_key == 'treble':
                effect_msg = f"🔇 Moins d'aigus ({value:.1f}dB)"
            elif band_key == 'mid':
                effect_msg = f"🔇 Voix en retrait ({value:.1f}dB)"
            else:
                effect_msg = f"🎚️ {band_info['name']} cut ({value:.1f}dB)"
        
        self.status_label.configure(text=effect_msg)
        
        # Effet visuel sur le volume pour simuler l'impact
        if abs(value) > 10:
            # Ajustement léger du volume pour simuler l'effet
            current_vol = self.volume_slider.get()
            if value > 0:
                # Boost léger
                new_vol = min(100, current_vol + 2)
            else:
                # Cut léger
                new_vol = max(0, current_vol - 2)
            
            # Application temporaire
            self.root.after(100, lambda: self.volume_slider.set(current_vol))

    def apply_preset(self, preset_values, preset_name):
        """Applique un preset"""
        for band_key, value in preset_values.items():
            if band_key in self.eq_sliders:
                self.eq_sliders[band_key].set(value)
                self.eq_bands[band_key]['value'] = value
                
                if value >= 0:
                    self.eq_value_labels[band_key].configure(text=f"+{value:.1f}dB")
                else:
                    self.eq_value_labels[band_key].configure(text=f"{value:.1f}dB")
        
        self.status_label.configure(text=f"⚡ Preset '{preset_name}' appliqué")

    def reset_equalizer(self):
        """Remet l'égaliseur à plat"""
        for band_key in self.eq_bands:
            self.eq_sliders[band_key].set(0)
            self.eq_bands[band_key]['value'] = 0
            self.eq_value_labels[band_key].configure(text="0dB")
        
        self.status_label.configure(text="🔄 Égaliseur remis à plat")

    def bass_boost(self):
        """Boost des basses"""
        self.eq_sliders['bass'].set(12)
        self.eq_sliders['low_mid'].set(6)
        self.eq_bands['bass']['value'] = 12
        self.eq_bands['low_mid']['value'] = 6
        self.eq_value_labels['bass'].configure(text="+12.0dB")
        self.eq_value_labels['low_mid'].configure(text="+6.0dB")
        
        self.status_label.configure(text="🔊 Bass Boost activé - Basses renforcées !")

    def vocal_enhance(self):
        """Amélioration vocale"""
        self.eq_sliders['mid'].set(8)
        self.eq_sliders['high_mid'].set(6)
        self.eq_sliders['presence'].set(4)
        self.eq_bands['mid']['value'] = 8
        self.eq_bands['high_mid']['value'] = 6
        self.eq_bands['presence']['value'] = 4
        self.eq_value_labels['mid'].configure(text="+8.0dB")
        self.eq_value_labels['high_mid'].configure(text="+6.0dB")
        self.eq_value_labels['presence'].configure(text="+4.0dB")
        
        self.status_label.configure(text="🎤 Vocal Enhance activé - Voix plus claire !")

    def run(self):
        """Lance l'application"""
        # Centrer la fenêtre
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        self.root.mainloop()

if __name__ == "__main__":
    app = WorkingEqualizer()
    app.run()
