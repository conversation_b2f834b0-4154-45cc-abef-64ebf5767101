@echo off
echo Testing AI Equalizer Application...

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    echo Please install Flutter from https://flutter.dev/docs/get-started/install/windows
    pause
    exit /b 1
)

REM Get dependencies
echo Getting dependencies...
flutter pub get

REM Run tests
echo Running unit tests...
flutter test

REM Check if tests passed
if %errorlevel% equ 0 (
    echo.
    echo All tests passed!
    echo.
    echo You can now:
    echo 1. Run in debug mode: run_debug.bat
    echo 2. Build release version: build.bat
    echo.
) else (
    echo.
    echo Some tests failed!
    echo Please check the error messages above.
    echo.
)

pause
