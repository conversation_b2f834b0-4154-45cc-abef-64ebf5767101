import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/equalizer_provider.dart';
import '../services/ai_service.dart';

class AIPanel extends StatelessWidget {
  const AIPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EqualizerProvider>(
      builder: (context, eqProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Contrôles IA
              _buildAIControls(context, eqProvider),
              const SizedBox(height: 20),

              // Détection de genre
              Expanded(
                flex: 2,
                child: _buildGenreDetection(context, eqProvider),
              ),

              const SizedBox(height: 20),

              // Confidences des genres
              Expanded(
                flex: 3,
                child: _buildGenreConfidences(context, eqProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAIControls(BuildContext context, EqualizerProvider eqProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.smart_toy, color: Colors.blue, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Intelligence Artificielle',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: eqProvider.isAnalyzing ? Colors.green : Colors.grey,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      eqProvider.isAnalyzing ? Icons.circle : Icons.circle_outlined,
                      size: 8,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      eqProvider.isAnalyzing ? 'Actif' : 'Inactif',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              // Activation de l'analyse IA
              Expanded(
                child: Row(
                  children: [
                    Switch(
                      value: eqProvider.aiAnalysisEnabled,
                      onChanged: (value) => eqProvider.setAIAnalysisEnabled(value),
                      activeColor: Colors.blue,
                    ),
                    const SizedBox(width: 8),
                    const Text('Analyse IA'),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Mode automatique
              Expanded(
                child: Row(
                  children: [
                    Switch(
                      value: eqProvider.autoMode,
                      onChanged: eqProvider.aiAnalysisEnabled
                        ? (value) => eqProvider.setAutoMode(value)
                        : null,
                      activeColor: Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Auto-ajustement',
                      style: TextStyle(
                        color: eqProvider.aiAnalysisEnabled
                          ? Colors.white
                          : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (!eqProvider.aiAnalysisEnabled)
            Container(
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.orange, size: 16),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Activez l\'analyse IA pour la détection automatique des genres musicaux',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGenreDetection(BuildContext context, EqualizerProvider eqProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.music_note, color: Colors.green),
              const SizedBox(width: 8),
              const Text(
                'Genre Détecté',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (eqProvider.isAnalyzing)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 20),

          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getGenreColor(eqProvider.currentGenre).withOpacity(0.2),
                      border: Border.all(
                        color: _getGenreColor(eqProvider.currentGenre),
                        width: 3,
                      ),
                    ),
                    child: Icon(
                      _getGenreIcon(eqProvider.currentGenre),
                      size: 48,
                      color: _getGenreColor(eqProvider.currentGenre),
                    ),
                  ),

                  const SizedBox(height: 16),

                  Text(
                    eqProvider.getCurrentGenreName(),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: _getGenreColor(eqProvider.currentGenre),
                    ),
                  ),

                  const SizedBox(height: 8),

                  if (eqProvider.currentGenre != MusicGenre.unknown)
                    Text(
                      'Confidence: ${(eqProvider.getGenreConfidence(eqProvider.currentGenre) * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[400],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenreConfidences(BuildContext context, EqualizerProvider eqProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Icon(Icons.analytics, color: Colors.purple),
              SizedBox(width: 8),
              Text(
                'Analyse des Genres',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Expanded(
            child: eqProvider.genreConfidences.isEmpty
              ? const Center(
                  child: Text(
                    'Aucune analyse disponible',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                )
              : ListView.builder(
                  itemCount: eqProvider.genreConfidences.length,
                  itemBuilder: (context, index) {
                    final entry = eqProvider.genreConfidences.entries.elementAt(index);
                    final genreName = entry.key;
                    final confidence = entry.value;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getGenreIconByName(genreName),
                                size: 16,
                                color: eqProvider.getGenreConfidenceColor(confidence),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                genreName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                '${(confidence * 100).toStringAsFixed(1)}%',
                                style: TextStyle(
                                  color: eqProvider.getGenreConfidenceColor(confidence),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: confidence,
                            backgroundColor: Colors.grey[700],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              eqProvider.getGenreConfidenceColor(confidence),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
          ),
        ],
      ),
    );
  }

  Color _getGenreColor(MusicGenre genre) {
    switch (genre) {
      case MusicGenre.rock:
        return Colors.red;
      case MusicGenre.pop:
        return Colors.pink;
      case MusicGenre.classical:
        return Colors.purple;
      case MusicGenre.jazz:
        return Colors.orange;
      case MusicGenre.electronic:
        return Colors.cyan;
      case MusicGenre.hiphop:
        return Colors.yellow;
      case MusicGenre.country:
        return Colors.brown;
      case MusicGenre.blues:
        return Colors.indigo;
      case MusicGenre.reggae:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getGenreIcon(MusicGenre genre) {
    switch (genre) {
      case MusicGenre.rock:
        return Icons.music_note;
      case MusicGenre.pop:
        return Icons.star;
      case MusicGenre.classical:
        return Icons.piano;
      case MusicGenre.jazz:
        return Icons.music_note;
      case MusicGenre.electronic:
        return Icons.computer;
      case MusicGenre.hiphop:
        return Icons.mic;
      case MusicGenre.country:
        return Icons.landscape;
      case MusicGenre.blues:
        return Icons.sentiment_very_dissatisfied;
      case MusicGenre.reggae:
        return Icons.waves;
      default:
        return Icons.help_outline;
    }
  }

  IconData _getGenreIconByName(String genreName) {
    switch (genreName.toLowerCase()) {
      case 'rock':
        return Icons.music_note;
      case 'pop':
        return Icons.star;
      case 'classical':
        return Icons.piano;
      case 'jazz':
        return Icons.music_note;
      case 'electronic':
        return Icons.computer;
      case 'hip-hop':
        return Icons.mic;
      case 'country':
        return Icons.landscape;
      case 'blues':
        return Icons.sentiment_very_dissatisfied;
      case 'reggae':
        return Icons.waves;
      default:
        return Icons.help_outline;
    }
  }
}
