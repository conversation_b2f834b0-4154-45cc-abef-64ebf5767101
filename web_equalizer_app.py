#!/usr/bin/env python3
"""
AI Equalizer Web - Interface Chrome
Application web avec Flask qui s'ouvre dans le navigateur
"""

import asyncio
import json
import os
import threading
import time
from datetime import datetime
from pathlib import Path

try:
    import numpy as np
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import Socket<PERSON>, emit
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    WEB_AVAILABLE = True
    print("✅ Modules web disponibles")
except ImportError as e:
    print(f"❌ Modules manquants: {e}")
    print("Installation: pip install flask flask-socketio numpy pycaw")
    WEB_AVAILABLE = False

class WebEqualizerApp:
    def __init__(self):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'equalizer_secret_key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Variables d'état
        self.is_listening = False
        self.sample_rate = 44100
        self.chunk_size = 1024
        
        # Paramètres d'égaliseur
        self.eq_bands = {
            'bass': {'name': 'Bass', 'freq': '60Hz', 'value': 0.0},
            'low_mid': {'name': 'Low Mid', 'freq': '170Hz', 'value': 0.0},
            'mid': {'name': 'Mid', 'freq': '350Hz', 'value': 0.0},
            'high_mid': {'name': 'High Mid', 'freq': '1kHz', 'value': 0.0},
            'treble': {'name': 'Treble', 'freq': '3.5kHz', 'value': 0.0},
            'presence': {'name': 'Presence', 'freq': '8kHz', 'value': 0.0}
        }
        
        # Détection de genre
        self.current_genre = "Unknown"
        self.genre_confidence = 0.0
        self.audio_level = 0.0
        
        # Contrôle volume système
        self.volume_control = None
        self.master_volume = 1.0
        
        # Configuration des routes
        self.setup_routes()
        self.setup_socketio()
        
        # Initialisation audio
        if WEB_AVAILABLE:
            self.setup_volume_control()

    def setup_routes(self):
        """Configuration des routes Flask"""
        
        @self.app.route('/')
        def index():
            return render_template('equalizer.html')
        
        @self.app.route('/api/equalizer', methods=['GET', 'POST'])
        def equalizer_api():
            if request.method == 'GET':
                return jsonify({
                    'bands': self.eq_bands,
                    'master_volume': self.master_volume,
                    'is_listening': self.is_listening,
                    'current_genre': self.current_genre,
                    'genre_confidence': self.genre_confidence,
                    'audio_level': self.audio_level
                })
            
            elif request.method == 'POST':
                data = request.json
                
                if 'band' in data and 'value' in data:
                    # Mise à jour d'une bande
                    band = data['band']
                    value = float(data['value'])
                    if band in self.eq_bands:
                        self.eq_bands[band]['value'] = value
                        self.apply_eq_band(band, value)
                        return jsonify({'success': True})
                
                elif 'master_volume' in data:
                    # Mise à jour du volume maître
                    self.master_volume = float(data['master_volume'])
                    self.set_system_volume(self.master_volume)
                    return jsonify({'success': True})
                
                elif 'preset' in data:
                    # Application d'un preset
                    self.apply_preset(data['preset'])
                    return jsonify({'success': True, 'bands': self.eq_bands})
                
                elif 'auto_adjust' in data:
                    # Ajustement automatique
                    self.auto_adjust_equalizer()
                    return jsonify({'success': True, 'bands': self.eq_bands})
                
                return jsonify({'error': 'Invalid request'}), 400
        
        @self.app.route('/api/audio/start', methods=['POST'])
        def start_audio():
            self.start_audio_simulation()
            return jsonify({'success': True})
        
        @self.app.route('/api/audio/stop', methods=['POST'])
        def stop_audio():
            self.stop_audio_simulation()
            return jsonify({'success': True})

    def setup_socketio(self):
        """Configuration des événements SocketIO"""
        
        @self.socketio.on('connect')
        def handle_connect():
            print(f"Client connecté: {request.sid}")
            emit('status', {
                'connected': True,
                'audio_available': WEB_AVAILABLE
            })
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client déconnecté: {request.sid}")

    def setup_volume_control(self):
        """Initialisation du contrôle volume système"""
        try:
            devices = AudioUtilities.GetSpeakers()
            interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
            self.volume_control = interface.QueryInterface(IAudioEndpointVolume)
            
            # Volume actuel
            self.volume_range = self.volume_control.GetVolumeRange()
            current_vol_db = self.volume_control.GetMasterVolumeLevel()
            self.master_volume = self.db_to_percent(current_vol_db) / 100.0
            
            print("✅ Contrôle volume système activé")
            
        except Exception as e:
            print(f"⚠️ Contrôle volume non disponible: {e}")
            self.volume_control = None

    def db_to_percent(self, db_value):
        """Convertit dB en pourcentage"""
        if not hasattr(self, 'volume_range') or not self.volume_range:
            return 50
        min_vol, max_vol, _ = self.volume_range
        percent = ((db_value - min_vol) / (max_vol - min_vol)) * 100
        return max(0, min(100, int(percent)))

    def percent_to_db(self, percent):
        """Convertit pourcentage en dB"""
        if not hasattr(self, 'volume_range') or not self.volume_range:
            return -20.0
        min_vol, max_vol, _ = self.volume_range
        return min_vol + (percent / 100.0) * (max_vol - min_vol)

    def start_audio_simulation(self):
        """Démarre la simulation audio"""
        if self.is_listening:
            return
        
        self.is_listening = True
        
        # Thread de simulation
        self.audio_thread = threading.Thread(target=self.audio_simulation_loop, daemon=True)
        self.audio_thread.start()
        
        # Notifier les clients
        self.socketio.emit('audio_started')
        print("🎧 Simulation audio démarrée")

    def stop_audio_simulation(self):
        """Arrête la simulation audio"""
        self.is_listening = False
        self.socketio.emit('audio_stopped')
        print("⏹️ Simulation audio arrêtée")

    def audio_simulation_loop(self):
        """Boucle de simulation audio"""
        import random
        
        while self.is_listening:
            try:
                # Simulation du niveau audio
                self.audio_level = random.uniform(0.1, 0.8)
                
                # Simulation de détection de genre
                genres = ["Rock", "Pop", "Electronic", "Classical", "Jazz", "Ambient"]
                if random.random() < 0.1:  # Changement de genre occasionnel
                    self.current_genre = random.choice(genres)
                    self.genre_confidence = random.uniform(70, 95)
                
                # Simulation de forme d'onde
                waveform = [random.uniform(-0.5, 0.5) for _ in range(50)]
                
                # Envoi des données
                self.socketio.emit('audio_data', {
                    'level': self.audio_level,
                    'genre': self.current_genre,
                    'confidence': self.genre_confidence,
                    'waveform': waveform
                })
                
                time.sleep(0.1)  # 10 FPS
                
            except Exception as e:
                print(f"❌ Erreur simulation: {e}")
                break

    def apply_eq_band(self, band, value):
        """Applique le réglage d'une bande d'égalisation"""
        print(f"🎛️ {band}: {value:+.1f}dB")
        
        # Notification temps réel
        self.socketio.emit('eq_changed', {
            'band': band,
            'value': value
        })

    def set_system_volume(self, volume):
        """Définit le volume système"""
        try:
            if self.volume_control:
                volume_db = self.percent_to_db(volume * 100)
                self.volume_control.SetMasterVolumeLevel(volume_db, None)
                print(f"🔊 Volume système: {volume*100:.0f}%")
                
                self.socketio.emit('volume_changed', {
                    'volume': volume
                })
                
        except Exception as e:
            print(f"❌ Erreur volume: {e}")

    def apply_preset(self, preset_name):
        """Applique un preset d'égaliseur"""
        presets = {
            'flat': {'bass': 0, 'low_mid': 0, 'mid': 0, 'high_mid': 0, 'treble': 0, 'presence': 0},
            'rock': {'bass': 6, 'low_mid': 2, 'mid': -1, 'high_mid': 3, 'treble': 4, 'presence': 2},
            'pop': {'bass': 2, 'low_mid': 0, 'mid': 1, 'high_mid': 2, 'treble': 2, 'presence': 1},
            'electronic': {'bass': 8, 'low_mid': 3, 'mid': -2, 'high_mid': 1, 'treble': 5, 'presence': 3},
            'classical': {'bass': 0, 'low_mid': 0, 'mid': 0, 'high_mid': 1, 'treble': 2, 'presence': 1},
            'jazz': {'bass': 1, 'low_mid': 3, 'mid': 2, 'high_mid': 0, 'treble': 1, 'presence': 0},
            'vocal': {'bass': -2, 'low_mid': 1, 'mid': 3, 'high_mid': 4, 'treble': 2, 'presence': 3}
        }
        
        if preset_name in presets:
            preset = presets[preset_name]
            for band, value in preset.items():
                if band in self.eq_bands:
                    self.eq_bands[band]['value'] = value
                    self.apply_eq_band(band, value)
            
            print(f"⚡ Preset '{preset_name}' appliqué")

    def auto_adjust_equalizer(self):
        """Ajustement automatique basé sur le genre détecté"""
        genre_presets = {
            'Rock': 'rock',
            'Pop': 'pop',
            'Electronic': 'electronic',
            'Classical': 'classical',
            'Jazz': 'jazz',
            'Ambient': 'vocal'
        }
        
        preset = genre_presets.get(self.current_genre, 'flat')
        self.apply_preset(preset)
        
        self.socketio.emit('auto_adjusted', {
            'genre': self.current_genre,
            'preset': preset,
            'bands': self.eq_bands
        })

    def run(self, host='127.0.0.1', port=5000, debug=False):
        """Lance l'application"""
        print(f"🚀 AI Equalizer Web démarré")
        print(f"🌐 Interface web: http://{host}:{port}")
        
        self.socketio.run(self.app, host=host, port=port, debug=debug)

    def cleanup(self):
        """Nettoyage à la fermeture"""
        if self.is_listening:
            self.stop_audio_simulation()

if __name__ == "__main__":
    app = WebEqualizerApp()
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Arrêt de l'application...")
        app.cleanup()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        app.cleanup()
