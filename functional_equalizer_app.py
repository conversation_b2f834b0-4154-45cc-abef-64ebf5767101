#!/usr/bin/env python3
"""
AI Equalizer Fonctionnel - Traitement Audio Réel
Interface moderne avec égaliseur qui fonctionne vraiment
"""

import customtkinter as ctk
import threading
import time
import math
import numpy as np
from scipy import signal
import queue

try:
    import sounddevice as sd
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    AUDIO_AVAILABLE = True
except ImportError:
    AUDIO_AVAILABLE = False

# Configuration du thème moderne
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class FunctionalEqualizerApp:
    def __init__(self):
        # Fenêtre principale
        self.root = ctk.CTk()
        self.root.title("🎵 AI Equalizer - Fonctionnel")
        self.root.geometry("700x650")
        self.root.resizable(False, False)
        
        # Variables de contrôle volume
        self.volume_control = None
        self.current_volume = 50
        self.is_muted = False
        self.volume_range = None
        
        # Variables audio
        self.is_processing = False
        self.sample_rate = 44100
        self.block_size = 512
        self.input_device = None
        self.output_device = None
        self.audio_stream = None
        
        # Variables d'égaliseur avec filtres
        self.eq_bands = {
            'bass': {'name': 'Bass', 'freq': 60, 'gain': 0, 'q': 1.0, 'color': '#f44336', 'filter': None},
            'low_mid': {'name': 'Low Mid', 'freq': 170, 'gain': 0, 'q': 1.0, 'color': '#ff9800', 'filter': None},
            'mid': {'name': 'Mid', 'freq': 350, 'gain': 0, 'q': 1.0, 'color': '#ffeb3b', 'filter': None},
            'high_mid': {'name': 'High Mid', 'freq': 1000, 'gain': 0, 'q': 1.0, 'color': '#4caf50', 'filter': None},
            'treble': {'name': 'Treble', 'freq': 3500, 'gain': 0, 'q': 1.0, 'color': '#2196f3', 'filter': None},
            'presence': {'name': 'Presence', 'freq': 8000, 'gain': 0, 'q': 1.0, 'color': '#9c27b0', 'filter': None}
        }
        
        # Initialisation des filtres
        self.init_eq_filters()
        
        # Interface
        self.create_interface()
        
        # Initialisation
        if AUDIO_AVAILABLE:
            self.init_volume_control()
            self.scan_audio_devices()
        else:
            self.show_error("Modules audio non disponibles")

    def init_eq_filters(self):
        """Initialise les filtres d'égalisation"""
        for band_key, band_info in self.eq_bands.items():
            # Filtre passe-bande pour chaque fréquence
            freq = band_info['freq']
            q = band_info['q']
            
            # Calcul des coefficients du filtre
            nyquist = self.sample_rate / 2
            normalized_freq = freq / nyquist
            
            # Filtre IIR passe-bande
            sos = signal.butter(2, [normalized_freq * 0.7, normalized_freq * 1.3], 
                               btype='band', output='sos')
            band_info['filter'] = sos

    def create_interface(self):
        """Crée l'interface moderne"""
        
        # Frame principal
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # Titre
        title_label = ctk.CTkLabel(
            main_frame,
            text="🎵 AI Equalizer Fonctionnel",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        title_label.pack(pady=(15, 5))
        
        # Sous-titre
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Traitement Audio Réel avec Égaliseur",
            font=ctk.CTkFont(size=12),
            text_color=("gray70", "gray30")
        )
        subtitle_label.pack(pady=(0, 10))
        
        # Configuration audio
        self.create_audio_config(main_frame)
        
        # Contrôle volume
        self.create_volume_control(main_frame)
        
        # Égaliseur
        self.create_equalizer(main_frame)
        
        # Statut
        self.create_status(main_frame)

    def create_audio_config(self, parent):
        """Section configuration audio"""
        config_frame = ctk.CTkFrame(parent, corner_radius=15)
        config_frame.pack(fill="x", padx=20, pady=5)
        
        # Titre
        config_title = ctk.CTkLabel(
            config_frame,
            text="🎛️ Configuration Audio",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        config_title.pack(pady=(10, 5))
        
        # Sélection périphériques
        devices_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        devices_frame.pack(fill="x", padx=15, pady=5)
        
        # Entrée
        ctk.CTkLabel(devices_frame, text="Entrée:", font=ctk.CTkFont(size=11)).grid(row=0, column=0, sticky="w", padx=5)
        self.input_combo = ctk.CTkComboBox(devices_frame, width=200, values=["Chargement..."])
        self.input_combo.grid(row=0, column=1, padx=5, pady=2)
        
        # Sortie
        ctk.CTkLabel(devices_frame, text="Sortie:", font=ctk.CTkFont(size=11)).grid(row=1, column=0, sticky="w", padx=5)
        self.output_combo = ctk.CTkComboBox(devices_frame, width=200, values=["Chargement..."])
        self.output_combo.grid(row=1, column=1, padx=5, pady=2)
        
        # Boutons
        buttons_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        buttons_frame.pack(pady=10)
        
        self.start_btn = ctk.CTkButton(
            buttons_frame,
            text="🎧 Démarrer Traitement",
            command=self.start_audio_processing,
            fg_color=("#4caf50", "#388e3c"),
            width=150
        )
        self.start_btn.pack(side="left", padx=5)
        
        self.stop_btn = ctk.CTkButton(
            buttons_frame,
            text="⏹️ Arrêter",
            command=self.stop_audio_processing,
            fg_color=("#f44336", "#d32f2f"),
            width=150,
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=5)

    def create_volume_control(self, parent):
        """Section contrôle volume"""
        volume_frame = ctk.CTkFrame(parent, corner_radius=15)
        volume_frame.pack(fill="x", padx=20, pady=5)
        
        # Label volume
        self.volume_label = ctk.CTkLabel(
            volume_frame,
            text="Volume: 50%",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.volume_label.pack(pady=(10, 5))
        
        # Slider volume
        self.volume_slider = ctk.CTkSlider(
            volume_frame,
            from_=0, to=100,
            width=400,
            command=self.on_volume_change
        )
        self.volume_slider.set(50)
        self.volume_slider.pack(pady=(5, 10))

    def create_equalizer(self, parent):
        """Section égaliseur"""
        eq_frame = ctk.CTkFrame(parent, corner_radius=15)
        eq_frame.pack(fill="both", expand=True, padx=20, pady=5)
        
        # Titre
        eq_title = ctk.CTkLabel(
            eq_frame,
            text="🎚️ Égaliseur Fonctionnel",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        eq_title.pack(pady=(10, 5))
        
        # Sliders
        sliders_frame = ctk.CTkFrame(eq_frame, fg_color="transparent")
        sliders_frame.pack(fill="x", padx=15, pady=5)
        
        self.eq_sliders = {}
        self.eq_value_labels = {}
        
        for i, (band_key, band_info) in enumerate(self.eq_bands.items()):
            # Frame pour chaque bande
            band_frame = ctk.CTkFrame(sliders_frame, width=90, corner_radius=8)
            band_frame.pack(side="left", fill="y", padx=3, pady=5)
            band_frame.pack_propagate(False)
            
            # Nom
            name_label = ctk.CTkLabel(
                band_frame,
                text=band_info['name'],
                font=ctk.CTkFont(size=10, weight="bold"),
                text_color=band_info['color']
            )
            name_label.pack(pady=(8, 2))
            
            # Fréquence
            freq_label = ctk.CTkLabel(
                band_frame,
                text=f"{band_info['freq']}Hz",
                font=ctk.CTkFont(size=9)
            )
            freq_label.pack(pady=(0, 3))
            
            # Slider
            eq_slider = ctk.CTkSlider(
                band_frame,
                from_=20, to=-20,
                width=18, height=120,
                orientation="vertical",
                command=lambda v, k=band_key: self.on_eq_change(k, v),
                button_color=band_info['color'],
                progress_color=band_info['color']
            )
            eq_slider.set(0)
            eq_slider.pack(pady=3)
            
            # Valeur
            value_label = ctk.CTkLabel(
                band_frame,
                text="0dB",
                font=ctk.CTkFont(size=9),
                text_color=band_info['color']
            )
            value_label.pack(pady=(3, 8))
            
            self.eq_sliders[band_key] = eq_slider
            self.eq_value_labels[band_key] = value_label
        
        # Presets
        presets_frame = ctk.CTkFrame(eq_frame, corner_radius=8)
        presets_frame.pack(fill="x", padx=15, pady=(5, 10))
        
        ctk.CTkLabel(presets_frame, text="Presets:", font=ctk.CTkFont(size=11)).pack(side="left", padx=(10, 5), pady=8)
        
        presets = [
            ("Flat", {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 0, "treble": 0, "presence": 0}),
            ("Rock", {"bass": 6, "low_mid": 2, "mid": -1, "high_mid": 3, "treble": 4, "presence": 2}),
            ("Pop", {"bass": 3, "low_mid": 0, "mid": 1, "high_mid": 2, "treble": 2, "presence": 1}),
            ("Electronic", {"bass": 8, "low_mid": 3, "mid": -2, "high_mid": 1, "treble": 5, "presence": 3})
        ]
        
        for preset_name, preset_values in presets:
            preset_btn = ctk.CTkButton(
                presets_frame,
                text=preset_name,
                width=60, height=25,
                command=lambda p=preset_values: self.apply_preset(p),
                font=ctk.CTkFont(size=9)
            )
            preset_btn.pack(side="left", padx=2, pady=8)

    def create_status(self, parent):
        """Section statut"""
        status_frame = ctk.CTkFrame(parent, corner_radius=15)
        status_frame.pack(fill="x", padx=20, pady=5)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Prêt - Sélectionnez vos périphériques et démarrez",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=10)

    def scan_audio_devices(self):
        """Scan des périphériques audio"""
        try:
            devices = sd.query_devices()
            
            input_devices = []
            output_devices = []
            
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    input_devices.append(f"{i}: {device['name']}")
                if device['max_output_channels'] > 0:
                    output_devices.append(f"{i}: {device['name']}")
            
            self.input_combo.configure(values=input_devices)
            self.output_combo.configure(values=output_devices)
            
            if input_devices:
                self.input_combo.set(input_devices[0])
            if output_devices:
                self.output_combo.set(output_devices[0])
                
            self.status_label.configure(text=f"✅ {len(input_devices)} entrées, {len(output_devices)} sorties détectées")
            
        except Exception as e:
            self.show_error(f"Erreur scan périphériques: {e}")

    def start_audio_processing(self):
        """Démarre le traitement audio"""
        if self.is_processing:
            return
        
        try:
            # Récupération des périphériques
            input_text = self.input_combo.get()
            output_text = self.output_combo.get()
            
            if not input_text or not output_text:
                self.show_error("Sélectionnez les périphériques")
                return
            
            self.input_device = int(input_text.split(':')[0])
            self.output_device = int(output_text.split(':')[0])
            
            # Démarrage du stream
            self.is_processing = True
            
            # Thread de traitement
            self.audio_thread = threading.Thread(target=self.audio_processing_loop, daemon=True)
            self.audio_thread.start()
            
            # Interface
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.status_label.configure(text="🎧 Traitement audio actif avec égaliseur")
            
        except Exception as e:
            self.show_error(f"Erreur démarrage: {e}")

    def stop_audio_processing(self):
        """Arrête le traitement audio"""
        self.is_processing = False
        
        if self.audio_stream:
            self.audio_stream.stop()
            self.audio_stream.close()
        
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.status_label.configure(text="⏹️ Traitement audio arrêté")

    def audio_processing_loop(self):
        """Boucle de traitement audio avec égaliseur"""
        try:
            def audio_callback(indata, outdata, frames, time, status):
                if status:
                    print(f"Audio status: {status}")
                
                # Copie des données d'entrée
                audio_data = indata.copy()
                
                # Application de l'égaliseur
                processed_audio = self.apply_equalizer(audio_data)
                
                # Sortie
                outdata[:] = processed_audio
            
            # Stream audio
            with sd.Stream(
                device=(self.input_device, self.output_device),
                samplerate=self.sample_rate,
                blocksize=self.block_size,
                channels=2,
                callback=audio_callback
            ):
                while self.is_processing:
                    time.sleep(0.1)
                    
        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"Erreur traitement: {e}"))
            self.root.after(0, self.stop_audio_processing)

    def apply_equalizer(self, audio_data):
        """Applique l'égaliseur aux données audio"""
        try:
            processed = audio_data.copy()
            
            # Application des filtres pour chaque bande
            for band_key, band_info in self.eq_bands.items():
                gain = band_info['gain']
                
                if gain != 0:
                    # Conversion gain dB vers amplitude
                    amplitude = 10 ** (gain / 20.0)
                    
                    # Application du filtre et du gain
                    if band_info['filter'] is not None:
                        for channel in range(processed.shape[1]):
                            # Filtrage
                            filtered = signal.sosfilt(band_info['filter'], processed[:, channel])
                            # Application du gain et mélange
                            processed[:, channel] += filtered * (amplitude - 1) * 0.3
            
            # Limitation pour éviter la saturation
            processed = np.clip(processed, -1.0, 1.0)
            
            return processed
            
        except Exception as e:
            return audio_data

    def on_eq_change(self, band_key, value):
        """Callback changement égaliseur"""
        eq_value = float(value)
        self.eq_bands[band_key]['gain'] = eq_value
        
        # Mise à jour du label
        if eq_value >= 0:
            self.eq_value_labels[band_key].configure(text=f"+{eq_value:.1f}dB")
        else:
            self.eq_value_labels[band_key].configure(text=f"{eq_value:.1f}dB")

    def apply_preset(self, preset_values):
        """Applique un preset"""
        for band_key, value in preset_values.items():
            if band_key in self.eq_sliders:
                self.eq_sliders[band_key].set(value)
                self.eq_bands[band_key]['gain'] = value
                
                if value >= 0:
                    self.eq_value_labels[band_key].configure(text=f"+{value:.1f}dB")
                else:
                    self.eq_value_labels[band_key].configure(text=f"{value:.1f}dB")

    def init_volume_control(self):
        """Initialise le contrôle volume"""
        try:
            devices = AudioUtilities.GetSpeakers()
            interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
            self.volume_control = interface.QueryInterface(IAudioEndpointVolume)
            self.volume_range = self.volume_control.GetVolumeRange()
        except:
            pass

    def on_volume_change(self, value):
        """Callback volume"""
        volume_percent = int(value)
        self.volume_label.configure(text=f"Volume: {volume_percent}%")
        
        if self.volume_control:
            try:
                min_vol, max_vol, _ = self.volume_range
                volume_db = min_vol + (volume_percent / 100.0) * (max_vol - min_vol)
                self.volume_control.SetMasterVolumeLevel(volume_db, None)
            except:
                pass

    def show_error(self, message):
        """Affiche une erreur"""
        self.status_label.configure(text=f"❌ {message}")

    def run(self):
        """Lance l'application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Fermeture"""
        if self.is_processing:
            self.stop_audio_processing()
        self.root.destroy()

if __name__ == "__main__":
    app = FunctionalEqualizerApp()
    app.run()
