# Guide d'Installation - AI Equalizer

## Prérequis Système

### Windows 10/11
- **Processeur** : x64 architecture
- **RAM** : 4 GB minimum, 8 GB recommandé
- **Espace disque** : 2 GB pour Flutter + 1 GB pour le projet
- **Connexion Internet** : Pour télécharger les dépendances

## Installation de Flutter

### Étape 1 : Télécharger Flutter
1. Allez sur [flutter.dev](https://flutter.dev/docs/get-started/install/windows)
2. Téléchargez le SDK Flutter pour Windows
3. Extrayez l'archive dans `C:\flutter`

### Étape 2 : Configurer le PATH
1. Ouvrez les **Variables d'environnement système**
2. Ajoutez `C:\flutter\bin` au PATH
3. Redémarrez votre terminal

### Étape 3 : Vérifier l'installation
```bash
flutter doctor
```

### Étape 4 : Installer Visual Studio (pour Windows)
1. Téléchargez **Visual Studio Community 2022**
2. Installez avec les composants :
   - **Desktop development with C++**
   - **Windows 10/11 SDK**

## Installation du Projet

### Méthode 1 : Clonage Git (si disponible)
```bash
git clone <repository-url>
cd ai_equalizer
```

### Méthode 2 : Téléchargement direct
1. Téléchargez tous les fichiers du projet
2. Placez-les dans un dossier `ai_equalizer`

## Configuration du Projet

### 1. Installer les dépendances
```bash
cd ai_equalizer
flutter pub get
```

### 2. Vérifier la configuration
```bash
flutter doctor
flutter config --enable-windows-desktop
```

## Test de l'Application

### Test Rapide (Mode Debug)
```bash
# Méthode 1 : Script automatique
run_debug.bat

# Méthode 2 : Commande manuelle
flutter run -d windows
```

### Tests Unitaires
```bash
# Méthode 1 : Script automatique
test_app.bat

# Méthode 2 : Commande manuelle
flutter test
```

### Compilation Release
```bash
# Méthode 1 : Script automatique
build.bat

# Méthode 2 : Commande manuelle
flutter build windows --release
```

## Structure du Projet

```
ai_equalizer/
├── lib/                    # Code source Dart
│   ├── main.dart          # Point d'entrée
│   ├── providers/         # Gestion d'état
│   ├── services/          # Services métier
│   ├── screens/           # Écrans principaux
│   └── widgets/           # Composants UI
├── windows/               # Configuration Windows
├── test/                  # Tests unitaires
├── assets/                # Ressources
├── pubspec.yaml          # Configuration Flutter
├── build.bat             # Script de compilation
├── run_debug.bat         # Script de debug
├── test_app.bat          # Script de test
└── README.md             # Documentation
```

## Résolution des Problèmes

### Erreur : "Flutter command not found"
**Solution :**
1. Vérifiez que Flutter est dans le PATH
2. Redémarrez votre terminal
3. Exécutez `flutter doctor`

### Erreur : "No supported devices"
**Solution :**
```bash
flutter config --enable-windows-desktop
flutter devices
```

### Erreur : "Visual Studio not found"
**Solution :**
1. Installez Visual Studio Community 2022
2. Incluez "Desktop development with C++"
3. Redémarrez et exécutez `flutter doctor`

### Erreur : "Pub get failed"
**Solution :**
```bash
flutter clean
flutter pub cache repair
flutter pub get
```

### Erreur de compilation Windows
**Solution :**
1. Vérifiez que Visual Studio est installé
2. Assurez-vous que Windows SDK est installé
3. Essayez :
```bash
flutter clean
flutter pub get
flutter build windows --verbose
```

## Première Utilisation

### 1. Lancer l'application
- **Debug** : `run_debug.bat` ou `flutter run -d windows`
- **Release** : `build.bat` puis exécuter `build\windows\runner\Release\ai_equalizer.exe`

### 2. Interface utilisateur
1. **Bouton Play/Pause** : Démarre/arrête le traitement audio
2. **Onglets** : Naviguez entre Égaliseur, Visualiseur, IA, Contrôles
3. **Mode compact** : Cliquez sur la flèche pour réduire l'interface

### 3. Fonctionnalités principales
- **Égaliseur** : Ajustez les 6 bandes de fréquences
- **IA** : Activez la détection automatique de genre
- **Normalisation** : Paramètres avancés dans l'onglet Contrôles

## Performance et Optimisation

### Recommandations
- **RAM** : 8 GB pour une utilisation fluide
- **CPU** : Processeur multi-cœurs recommandé
- **Audio** : Carte son dédiée pour de meilleures performances

### Paramètres optimaux
- **LUFS Cible** : -23 (broadcast) ou -16 (streaming)
- **Lissage** : 0.95 pour un bon équilibre
- **Gain Max** : 2.0 (+6dB) pour éviter la distorsion

## Support et Aide

### Logs et Debug
```bash
# Lancer avec logs détaillés
flutter run -d windows --verbose

# Voir les logs en temps réel
flutter logs
```

### Fichiers de log
- **Windows** : `%APPDATA%\ai_equalizer\logs\`
- **Debug** : Console Flutter

### Signaler un problème
1. Collectez les logs d'erreur
2. Notez votre configuration système
3. Décrivez les étapes pour reproduire le problème

## Mise à Jour

### Mettre à jour Flutter
```bash
flutter upgrade
flutter doctor
```

### Mettre à jour le projet
1. Téléchargez la nouvelle version
2. Sauvegardez vos paramètres si nécessaire
3. Exécutez `flutter pub get`
4. Recompilez avec `build.bat`

---

**Note importante** : Cette version utilise des simulations pour l'audio et l'IA. Pour une utilisation en production, des intégrations supplémentaires seront nécessaires.
