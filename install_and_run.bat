@echo off
echo ========================================
echo    AI EQUALIZER - INSTALLATION AUTO
echo ========================================
echo.

echo [1/4] Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Telechargez Python depuis: https://python.org
    pause
    exit /b 1
)
echo ✅ Python detecte

echo.
echo [2/4] Installation des modules Python...
echo Installation en cours... (cela peut prendre quelques minutes)

pip install pyaudio librosa scipy matplotlib pycaw numpy tkinter comtypes

if errorlevel 1 (
    echo.
    echo ⚠️ Erreur d'installation detectee
    echo Tentative avec des sources alternatives...
    pip install --upgrade pip
    pip install --user pyaudio librosa scipy matplotlib pycaw numpy comtypes
)

echo ✅ Modules installes

echo.
echo [3/4] Configuration audio Windows...
echo.
echo IMPORTANT: Pour capturer l'audio systeme, vous devez activer "Stereo Mix":
echo.
echo 1. Clic droit sur l'icone son (barre des taches)
echo 2. Ouvrir les parametres de son
echo 3. Panneau de configuration du son
echo 4. Onglet "Enregistrement"
echo 5. Clic droit dans la zone vide
echo 6. "Afficher les peripheriques desactives"
echo 7. Clic droit sur "Stereo Mix" ou "Mixage stereo"
echo 8. "Activer"
echo.
echo Appuyez sur une touche quand c'est fait...
pause

echo.
echo [4/4] Lancement de l'AI Equalizer...
echo.
echo 🎵 L'application va se lancer...
echo 🎧 Lancez votre musique YouTube et testez !
echo.

python real_audio_equalizer.py

echo.
echo Application fermee.
pause
