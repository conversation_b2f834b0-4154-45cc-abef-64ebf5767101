import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import '../providers/audio_provider.dart';
import '../providers/equalizer_provider.dart';

class AudioVisualizer extends StatelessWidget {
  const AudioVisualizer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Métriques en temps réel
          _buildMetrics(context),
          const SizedBox(height: 20),
          
          // Visualiseur principal
          Expanded(
            flex: 3,
            child: _buildMainVisualizer(context),
          ),
          
          const SizedBox(height: 20),
          
          // Historique des bandes
          Expanded(
            flex: 2,
            child: _buildBandsHistory(context),
          ),
        ],
      ),
    );
  }

  Widget _buildMetrics(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Row(
            children: [
              // Volume actuel
              Expanded(
                child: _buildMetricCard(
                  'Volume',
                  audioProvider.getVolumeDisplayText(),
                  audioProvider.getVolumeColor(),
                  Icons.volume_up,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Gain appliqué
              Expanded(
                child: _buildMetricCard(
                  'Gain',
                  audioProvider.getGainDisplayText(),
                  audioProvider.getGainColor(),
                  Icons.trending_up,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // LUFS
              Expanded(
                child: _buildMetricCard(
                  'LUFS',
                  audioProvider.getLufsDisplayText(),
                  Colors.blue,
                  Icons.graphic_eq,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Volume moyen
              Expanded(
                child: _buildMetricCard(
                  'Moyenne',
                  '${(audioProvider.getAverageVolume() * 100).toStringAsFixed(1)}%',
                  Colors.orange,
                  Icons.analytics,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMetricCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainVisualizer(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Column(
            children: [
              // En-tête
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(Icons.graphic_eq, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Visualiseur Audio',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => audioProvider.clearHistory(),
                      icon: const Icon(Icons.clear_all),
                      tooltip: 'Effacer l\'historique',
                    ),
                  ],
                ),
              ),
              
              // Visualiseur
              Expanded(
                child: CustomPaint(
                  painter: _AudioVisualizerPainter(
                    volumeHistory: audioProvider.volumeHistory,
                    bandsHistory: audioProvider.bandsHistory,
                    currentVolume: audioProvider.currentVolume,
                  ),
                  size: Size.infinite,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBandsHistory(BuildContext context) {
    return Consumer2<AudioProvider, EqualizerProvider>(
      builder: (context, audioProvider, eqProvider, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Column(
            children: [
              // En-tête
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(Icons.equalizer, color: Colors.green),
                    const SizedBox(width: 8),
                    const Text(
                      'Historique des Bandes',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => eqProvider.clearHistory(),
                      icon: const Icon(Icons.clear_all),
                      tooltip: 'Effacer l\'historique',
                    ),
                  ],
                ),
              ),
              
              // Graphique des bandes
              Expanded(
                child: CustomPaint(
                  painter: _BandsHistoryPainter(
                    bandsHistory: eqProvider.bandsHistory,
                    bands: eqProvider.bands,
                  ),
                  size: Size.infinite,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _AudioVisualizerPainter extends CustomPainter {
  final List<double> volumeHistory;
  final List<Map<String, double>> bandsHistory;
  final double currentVolume;

  _AudioVisualizerPainter({
    required this.volumeHistory,
    required this.bandsHistory,
    required this.currentVolume,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Dessiner la grille
    _drawGrid(canvas, size);

    // Dessiner l'historique du volume
    _drawVolumeHistory(canvas, size, paint);

    // Dessiner les bandes de fréquences
    _drawFrequencyBands(canvas, size, paint);

    // Dessiner l'indicateur de volume actuel
    _drawCurrentVolume(canvas, size, paint);
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey[700]!
      ..strokeWidth = 0.5;

    // Lignes horizontales
    for (int i = 0; i <= 10; i++) {
      final y = (i / 10) * size.height;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }

    // Lignes verticales
    for (int i = 0; i <= 20; i++) {
      final x = (i / 20) * size.width;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }

  void _drawVolumeHistory(Canvas canvas, Size size, Paint paint) {
    if (volumeHistory.isEmpty) return;

    final path = Path();
    final stepX = size.width / max(volumeHistory.length, 1);

    for (int i = 0; i < volumeHistory.length; i++) {
      final x = i * stepX;
      final y = size.height - (volumeHistory[i] * size.height);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    paint.color = Colors.blue;
    paint.strokeWidth = 3;
    canvas.drawPath(path, paint);

    // Remplissage sous la courbe
    final fillPath = Path.from(path);
    fillPath.lineTo(size.width, size.height);
    fillPath.lineTo(0, size.height);
    fillPath.close();

    final fillPaint = Paint()
      ..color = Colors.blue.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    canvas.drawPath(fillPath, fillPaint);
  }

  void _drawFrequencyBands(Canvas canvas, Size size, Paint paint) {
    if (bandsHistory.isEmpty) return;

    final bandNames = bandsHistory.first.keys.toList();
    final colors = [
      Colors.red,
      Colors.orange,
      Colors.yellow,
      Colors.green,
      Colors.cyan,
      Colors.purple,
    ];

    for (int bandIndex = 0; bandIndex < bandNames.length && bandIndex < colors.length; bandIndex++) {
      final bandName = bandNames[bandIndex];
      final path = Path();
      final stepX = size.width / max(bandsHistory.length, 1);

      for (int i = 0; i < bandsHistory.length; i++) {
        final value = bandsHistory[i][bandName] ?? 0.0;
        final x = i * stepX;
        final y = size.height - (value * size.height * 0.3) - size.height * 0.1;

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      paint.color = colors[bandIndex].withOpacity(0.8);
      paint.strokeWidth = 1.5;
      canvas.drawPath(path, paint);
    }
  }

  void _drawCurrentVolume(Canvas canvas, Size size, Paint paint) {
    final y = size.height - (currentVolume * size.height);
    
    paint.color = Colors.white;
    paint.strokeWidth = 2;
    paint.style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(size.width - 20, y),
      Offset(size.width, y),
      paint,
    );

    // Indicateur circulaire
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(size.width - 10, y),
      4,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _BandsHistoryPainter extends CustomPainter {
  final List<List<double>> bandsHistory;
  final List<dynamic> bands;

  _BandsHistoryPainter({
    required this.bandsHistory,
    required this.bands,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (bandsHistory.isEmpty || bands.isEmpty) return;

    final paint = Paint()
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final colors = [
      Colors.red,
      Colors.orange,
      Colors.yellow,
      Colors.green,
      Colors.cyan,
      Colors.purple,
    ];

    // Dessiner chaque bande
    for (int bandIndex = 0; bandIndex < bands.length && bandIndex < colors.length; bandIndex++) {
      final path = Path();
      final stepX = size.width / max(bandsHistory.length, 1);

      for (int i = 0; i < bandsHistory.length; i++) {
        if (bandIndex < bandsHistory[i].length) {
          final gain = bandsHistory[i][bandIndex];
          final x = i * stepX;
          final y = size.height / 2 - (gain / 20.0) * (size.height / 2);

          if (i == 0) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }
      }

      paint.color = colors[bandIndex];
      canvas.drawPath(path, paint);
    }

    // Ligne de référence (0 dB)
    final referencePaint = Paint()
      ..color = Colors.grey[600]!
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, size.height / 2),
      Offset(size.width, size.height / 2),
      referencePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
