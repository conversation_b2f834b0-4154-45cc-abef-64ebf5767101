import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import '../providers/audio_provider.dart';
import '../providers/equalizer_provider.dart';

class ControlPanel extends StatelessWidget {
  const ControlPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Contrôles de normalisation
          Expanded(
            flex: 2,
            child: _buildNormalizationControls(context),
          ),

          const SizedBox(height: 20),

          // Paramètres avancés
          Expanded(
            flex: 2,
            child: _buildAdvancedSettings(context),
          ),

          const SizedBox(height: 20),

          // Actions
          Expanded(
            flex: 1,
            child: _buildActions(context),
          ),
        ],
      ),
    );
  }

  Widget _buildNormalizationControls(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.tune, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'Normalisation Audio',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Target LUFS
              _buildSliderControl(
                context,
                'LUFS Cible',
                audioProvider.targetLufs,
                -60.0,
                0.0,
                (value) => audioProvider.setTargetLufs(value),
                '${audioProvider.targetLufs.toStringAsFixed(1)} LUFS',
                Colors.blue,
              ),

              const SizedBox(height: 16),

              // Alpha (lissage)
              _buildSliderControl(
                context,
                'Lissage (Alpha)',
                audioProvider.alpha,
                0.1,
                0.99,
                (value) => audioProvider.setNormalizationParams(alpha: value),
                audioProvider.alpha.toStringAsFixed(3),
                Colors.green,
              ),

              const SizedBox(height: 16),

              // Gain maximum
              _buildSliderControl(
                context,
                'Gain Maximum',
                audioProvider.maxGain,
                1.0,
                10.0,
                (value) => audioProvider.setNormalizationParams(maxGain: value),
                '+${(20 * (log(audioProvider.maxGain) / ln10)).toStringAsFixed(1)} dB',
                Colors.orange,
              ),

              const SizedBox(height: 16),

              // Gain minimum
              _buildSliderControl(
                context,
                'Gain Minimum',
                audioProvider.minGain,
                0.01,
                1.0,
                (value) => audioProvider.setNormalizationParams(minGain: value),
                '${(20 * (log(audioProvider.minGain) / ln10)).toStringAsFixed(1)} dB',
                Colors.red,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSliderControl(
    BuildContext context,
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
    String displayValue,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            Text(
              displayValue,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: color,
            inactiveTrackColor: Colors.grey[700],
            thumbColor: color,
            overlayColor: color.withOpacity(0.2),
            trackHeight: 4,
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.settings, color: Colors.purple),
              SizedBox(width: 8),
              Text(
                'Paramètres Avancés',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Presets de normalisation
          Consumer<AudioProvider>(
            builder: (context, audioProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Presets de Normalisation',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: audioProvider.getAvailablePresets().map((preset) {
                      return ElevatedButton(
                        onPressed: () => audioProvider.applyPreset(preset),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[800],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        ),
                        child: Text(preset),
                      );
                    }).toList(),
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 20),

          // Statistiques
          Consumer<AudioProvider>(
            builder: (context, audioProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Statistiques',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'Volume Moyen',
                          '${(audioProvider.getAverageVolume() * 100).toStringAsFixed(1)}%',
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'Pic Maximum',
                          '${(audioProvider.getPeakVolume() * 100).toStringAsFixed(1)}%',
                          Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[400],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Icon(Icons.build, color: Colors.orange),
              SizedBox(width: 8),
              Text(
                'Actions',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              // Effacer l'historique
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    context.read<AudioProvider>().clearHistory();
                    context.read<EqualizerProvider>().clearHistory();
                  },
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Effacer Historique'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[800],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Exporter les paramètres
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _exportSettings(context),
                  icon: const Icon(Icons.download),
                  label: const Text('Exporter'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[800],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Importer les paramètres
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _importSettings(context),
                  icon: const Icon(Icons.upload),
                  label: const Text('Importer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[800],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _exportSettings(BuildContext context) {
    final eqProvider = context.read<EqualizerProvider>();
    final settings = eqProvider.exportSettings();

    // Dans une vraie implémentation, on sauvegarderait dans un fichier
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Paramètres exportés (fonctionnalité à implémenter)'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _importSettings(BuildContext context) {
    // Dans une vraie implémentation, on chargerait depuis un fichier
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Import de paramètres (fonctionnalité à implémenter)'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
