import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/system_equalizer.dart';
import '../services/real_audio_capture.dart';

class SystemEqualizerWidget extends StatefulWidget {
  const SystemEqualizerWidget({super.key});

  @override
  State<SystemEqualizerWidget> createState() => _SystemEqualizerWidgetState();
}

class _SystemEqualizerWidgetState extends State<SystemEqualizerWidget> {
  late SystemEqualizer _systemEqualizer;
  late RealAudioCapture _audioCapture;
  
  bool _isListening = false;
  String _currentGenre = 'Unknown';
  Map<String, double> _detectedAdjustments = {};

  @override
  void initState() {
    super.initState();
    _systemEqualizer = context.read<SystemEqualizer>();
    _audioCapture = context.read<RealAudioCapture>();
    _setupListeners();
  }

  void _setupListeners() {
    // Écouter les analyses audio pour ajustements automatiques
    _audioCapture.analysisStream.listen((analysis) {
      if (mounted) {
        setState(() {
          _currentGenre = analysis['detectedGenre'] ?? 'Unknown';
          _detectedAdjustments = Map<String, double>.from(
            analysis['equalizerAdjustments'] ?? {}
          );
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // En-tête avec contrôles système
          _buildSystemHeader(),
          const SizedBox(height: 20),
          
          // Contrôles d'écoute en temps réel
          _buildListeningControls(),
          const SizedBox(height: 20),
          
          // Égaliseur système
          Expanded(
            child: _buildSystemEqualizer(),
          ),
          
          const SizedBox(height: 20),
          
          // Actions rapides
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildSystemHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red),
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Icon(Icons.volume_up, color: Colors.red, size: 28),
              SizedBox(width: 12),
              Text(
                'Égaliseur Système (Affecte YouTube)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Cet égaliseur modifie directement l\'audio de votre système Windows',
            style: TextStyle(
              fontSize: 12,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 12),
          
          // Volume maître système
          Row(
            children: [
              const Text('Volume Système: '),
              Expanded(
                child: StreamBuilder<Map<String, dynamic>>(
                  stream: _systemEqualizer.statusStream,
                  builder: (context, snapshot) {
                    final volume = snapshot.data?['masterVolume'] ?? 1.0;
                    return Slider(
                      value: volume,
                      min: 0.0,
                      max: 2.0,
                      divisions: 20,
                      label: '${(volume * 100).toStringAsFixed(0)}%',
                      activeColor: Colors.red,
                      onChanged: (value) {
                        _systemEqualizer.setMasterVolume(value);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildListeningControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.hearing, color: Colors.blue),
              const SizedBox(width: 8),
              const Text(
                'Écoute en Temps Réel',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              
              // Bouton d'écoute
              ElevatedButton.icon(
                onPressed: () async {
                  if (_isListening) {
                    _audioCapture.stopCapture();
                  } else {
                    await _audioCapture.startCapture();
                  }
                  setState(() {
                    _isListening = !_isListening;
                  });
                },
                icon: Icon(_isListening ? Icons.stop : Icons.play_arrow),
                label: Text(_isListening ? 'Arrêter' : 'Écouter'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isListening ? Colors.red : Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          
          if (_isListening) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                // Genre détecté
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withOpacity(0.3)),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'Genre Détecté',
                          style: TextStyle(fontSize: 12, color: Colors.blue),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _currentGenre,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Bouton d'application automatique
                ElevatedButton.icon(
                  onPressed: _detectedAdjustments.isNotEmpty ? () {
                    _systemEqualizer.applyEqualizerSettings(_detectedAdjustments);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Égaliseur ajusté pour $_currentGenre'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } : null,
                  icon: const Icon(Icons.auto_fix_high),
                  label: const Text('Appliquer Auto'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSystemEqualizer() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Icon(Icons.equalizer, color: Colors.green),
              SizedBox(width: 8),
              Text(
                'Bandes d\'Égalisation Système',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          Expanded(
            child: StreamBuilder<Map<String, dynamic>>(
              stream: _systemEqualizer.statusStream,
              builder: (context, snapshot) {
                final gains = snapshot.data?['gains'] ?? <String, double>{};
                
                return Row(
                  children: SystemEqualizer.bandNames.map((bandName) {
                    final gain = gains[bandName] ?? 0.0;
                    return Expanded(
                      child: _buildBandSlider(bandName, gain),
                    );
                  }).toList(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBandSlider(String bandName, double gain) {
    Color getColor(double gain) {
      if (gain > 0) return Colors.green;
      if (gain < 0) return Colors.red;
      return Colors.blue;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        children: [
          // Nom de la bande
          Text(
            bandName,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Slider vertical
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: getColor(gain),
                  inactiveTrackColor: Colors.grey[700],
                  thumbColor: getColor(gain),
                  overlayColor: getColor(gain).withOpacity(0.2),
                  trackHeight: 6,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: gain,
                  min: -20.0,
                  max: 20.0,
                  divisions: 80,
                  onChanged: (value) {
                    _systemEqualizer.setBandGain(bandName, value);
                  },
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Valeur du gain
          Text(
            '${gain >= 0 ? '+' : ''}${gain.toStringAsFixed(1)}dB',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: getColor(gain),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Row(
      children: [
        // Presets rapides
        Expanded(
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: ['Flat', 'Rock', 'Pop', 'Electronic', 'Classical', 'Jazz'].map((preset) {
              return ElevatedButton(
                onPressed: () {
                  _systemEqualizer.applyPreset(preset);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Preset $preset appliqué'),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[800],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: Text(preset, style: const TextStyle(fontSize: 12)),
              );
            }).toList(),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Reset
        ElevatedButton.icon(
          onPressed: () {
            _systemEqualizer.reset();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Égaliseur réinitialisé'),
                backgroundColor: Colors.orange,
              ),
            );
          },
          icon: const Icon(Icons.refresh),
          label: const Text('Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _audioCapture.stopCapture();
    super.dispose();
  }
}
