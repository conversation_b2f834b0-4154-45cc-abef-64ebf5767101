# 🚀 Quick Start - AI Equalizer

## Démarrage Rapide (5 minutes)

### 1. Vérification du Projet
```bash
# Double-cliquez sur ce fichier pour vérifier la structure
check_project.bat
```

### 2. Installation Flutter (si pas déjà fait)
1. **Téléchargez Flutter** : [flutter.dev](https://flutter.dev/docs/get-started/install/windows)
2. **Extrayez** dans `C:\flutter`
3. **Ajoutez au PATH** : `C:\flutter\bin`
4. **Vérifiez** : `flutter doctor`

### 3. Test Immédiat
```bash
# Option 1 : Test complet
test_app.bat

# Option 2 : Lancement direct en debug
run_debug.bat

# Option 3 : Compilation release
build.bat
```

## 🎯 Fonctionnalités Principales

### ✅ Implémenté et Fonctionnel
- **Interface utilisateur complète** avec 4 onglets
- **Égaliseur 6 bandes** avec contrôles en temps réel
- **Algorithmes de normalisation audio** (LUFS, AGC, compression)
- **Simulation IA** pour classification de genres musicaux
- **Visualiseur audio** en temps réel
- **Mode compact** pour fonctionnement en arrière-plan
- **Presets d'égalisation** (Rock, Pop, Classical, Jazz, etc.)
- **Paramètres avancés** de normalisation
- **Tests unitaires** complets

### 🔄 Simulé (pour démonstration)
- **Capture audio** (génère des signaux de test)
- **Classification IA** (utilise des algorithmes heuristiques)
- **Traitement audio** (applique les algorithmes mathématiques)

### 🚧 À Implémenter (version production)
- **Capture audio système réelle** (WASAPI/DirectSound)
- **Modèles IA entraînés** (TensorFlow Lite)
- **Service Windows** pour arrière-plan
- **Version Android** complète

## 🧮 Algorithmes Mathématiques Inclus

### Normalisation Automatique
```dart
// Automatic Gain Control (AGC)
gain(t) = target_level / current_level(t)
gain_smooth(t) = α × gain(t-1) + (1-α) × target_gain(t)

// LUFS Calculation
LUFS = -0.691 + 10 × log10(loudness)

// Dynamic Range Compression
if |input| > threshold:
    output = threshold + (|input| - threshold) / ratio
```

### Filtres d'Égalisation
```dart
// IIR Peaking Filter (Bell Filter)
y(n) = b0×x(n) + b1×x(n-1) + b2×x(n-2) - a1×y(n-1) - a2×y(n-2)

// Coefficients calculés automatiquement pour chaque bande
w = 2π × frequency / sampleRate
A = 10^(gain_dB / 40)
```

## 🎮 Guide d'Utilisation

### Interface Principale
1. **Bouton Play/Pause** : Active/désactive le traitement
2. **Indicateur LUFS** : Affiche le niveau de loudness en temps réel
3. **Mode compact** : Réduit l'interface pour l'arrière-plan

### Onglet Égaliseur
- **6 sliders verticaux** : Bass, Low Mid, Mid, High Mid, Treble, Presence
- **Gain maître** : Contrôle du volume global
- **Presets** : Rock, Pop, Classical, Jazz, Electronic, Vocal
- **Mode Auto** : Application automatique selon le genre détecté

### Onglet Visualiseur
- **Graphique temps réel** : Volume et bandes de fréquences
- **Métriques** : Volume, Gain, LUFS, Statistiques
- **Historique** : Évolution des paramètres

### Onglet IA
- **Détection de genre** : Classification automatique
- **Confidences** : Pourcentages pour chaque genre
- **Auto-ajustement** : Application automatique des presets

### Onglet Contrôles
- **Paramètres de normalisation** : LUFS cible, lissage, limites
- **Presets de normalisation** : Soft, Normal, Aggressive, Broadcast
- **Actions** : Export/import, effacement historique

## 🔧 Paramètres Recommandés

### Écoute Normale
- **LUFS Cible** : -23 (standard broadcast)
- **Lissage** : 0.95 (bon équilibre)
- **Gain Max** : +6dB (évite la distorsion)

### Environnement Bruyant
- **LUFS Cible** : -16 (plus fort)
- **Lissage** : 0.90 (plus réactif)
- **Preset** : "Aggressive"

### Écoute Relaxante
- **LUFS Cible** : -26 (plus doux)
- **Lissage** : 0.98 (très lisse)
- **Preset** : "Soft"

## 🐛 Résolution Rapide des Problèmes

### "Flutter command not found"
```bash
# Ajoutez Flutter au PATH et redémarrez le terminal
set PATH=%PATH%;C:\flutter\bin
```

### "No devices found"
```bash
flutter config --enable-windows-desktop
```

### Erreur de compilation
```bash
flutter clean
flutter pub get
flutter build windows --verbose
```

### Tests qui échouent
```bash
flutter pub deps
flutter test --verbose
```

## 📁 Structure du Code

```
lib/
├── main.dart                 # Point d'entrée
├── services/                 # Logique métier
│   ├── audio_service.dart    # Traitement audio + normalisation
│   ├── ai_service.dart       # Classification IA + presets
│   └── equalizer_service.dart # Filtres IIR + égalisation
├── providers/                # Gestion d'état
│   ├── audio_provider.dart   # État audio + normalisation
│   └── equalizer_provider.dart # État égaliseur + IA
├── screens/
│   └── main_screen.dart      # Interface principale
└── widgets/                  # Composants UI
    ├── equalizer_widget.dart # Interface égaliseur
    ├── audio_visualizer.dart # Visualisation temps réel
    ├── ai_panel.dart         # Contrôles IA
    └── control_panel.dart    # Paramètres avancés
```

## 🎯 Prochaines Étapes

### Pour Tester
1. `check_project.bat` - Vérifier la structure
2. `test_app.bat` - Lancer les tests
3. `run_debug.bat` - Tester l'interface

### Pour Développer
1. Intégrer une vraie capture audio (WASAPI)
2. Ajouter des modèles IA entraînés
3. Implémenter la sauvegarde de presets
4. Créer la version Android

### Pour Déployer
1. `build.bat` - Compiler la version release
2. Créer un installateur Windows
3. Optimiser les performances
4. Ajouter la signature de code

---

**🎉 Félicitations !** Vous avez maintenant une application d'égaliseur IA complète avec tous les algorithmes mathématiques de normalisation audio implémentés !
