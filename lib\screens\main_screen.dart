import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';
import '../providers/equalizer_provider.dart';
import '../widgets/equalizer_widget.dart';
import '../widgets/audio_visualizer.dart';
import '../widgets/ai_panel.dart';
import '../widgets/control_panel.dart';
import '../widgets/system_equalizer_widget.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isMinimized = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.equalizer, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('AI Equalizer'),
            const Spacer(),
            Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: audioProvider.getVolumeColor().withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: audioProvider.getVolumeColor()),
                  ),
                  child: Text(
                    audioProvider.getLufsDisplayText(),
                    style: TextStyle(
                      color: audioProvider.getVolumeColor(),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          Consumer<AudioProvider>(
            builder: (context, audioProvider, child) {
              return IconButton(
                icon: Icon(
                  audioProvider.isProcessing ? Icons.pause : Icons.play_arrow,
                  color: audioProvider.isProcessing ? Colors.red : Colors.green,
                ),
                onPressed: () {
                  if (audioProvider.isProcessing) {
                    audioProvider.stopProcessing();
                  } else {
                    audioProvider.startProcessing();
                  }
                },
                tooltip: audioProvider.isProcessing ? 'Arrêter' : 'Démarrer',
              );
            },
          ),
          IconButton(
            icon: Icon(_isMinimized ? Icons.expand_more : Icons.expand_less),
            onPressed: () {
              setState(() {
                _isMinimized = !_isMinimized;
              });
            },
            tooltip: _isMinimized ? 'Agrandir' : 'Réduire',
          ),
          const SizedBox(width: 8),
        ],
        bottom: _isMinimized ? null : TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.equalizer), text: 'Égaliseur'),
            Tab(icon: Icon(Icons.graphic_eq), text: 'Visualiseur'),
            Tab(icon: Icon(Icons.smart_toy), text: 'IA'),
            Tab(icon: Icon(Icons.volume_up, color: Colors.red), text: 'Système'),
            Tab(icon: Icon(Icons.settings), text: 'Contrôles'),
          ],
        ),
      ),
      body: _isMinimized ? _buildMinimizedView() : _buildFullView(),
    );
  }

  Widget _buildMinimizedView() {
    return Container(
      height: 120,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Visualiseur compact
          Expanded(
            flex: 2,
            child: Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomPaint(
                    painter: _CompactVisualizerPainter(
                      volumeHistory: audioProvider.volumeHistory,
                      bandsHistory: audioProvider.bandsHistory,
                    ),
                    size: const Size.fromHeight(80),
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 16),
          // Contrôles compacts
          Expanded(
            flex: 1,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Consumer<EqualizerProvider>(
                  builder: (context, eqProvider, child) {
                    return Row(
                      children: [
                        Icon(
                          Icons.smart_toy,
                          color: eqProvider.aiAnalysisEnabled ? Colors.blue : Colors.grey,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          eqProvider.getCurrentGenreName(),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    );
                  },
                ),
                Consumer<EqualizerProvider>(
                  builder: (context, eqProvider, child) {
                    return Row(
                      children: [
                        const Icon(Icons.equalizer, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          eqProvider.currentPreset,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    );
                  },
                ),
                Consumer<AudioProvider>(
                  builder: (context, audioProvider, child) {
                    return Row(
                      children: [
                        const Icon(Icons.volume_up, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          audioProvider.getGainDisplayText(),
                          style: TextStyle(
                            fontSize: 12,
                            color: audioProvider.getGainColor(),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullView() {
    return TabBarView(
      controller: _tabController,
      children: const [
        EqualizerWidget(),
        AudioVisualizer(),
        AIPanel(),
        SystemEqualizerWidget(),
        ControlPanel(),
      ],
    );
  }
}

class _CompactVisualizerPainter extends CustomPainter {
  final List<double> volumeHistory;
  final List<Map<String, double>> bandsHistory;

  _CompactVisualizerPainter({
    required this.volumeHistory,
    required this.bandsHistory,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Dessiner l'historique du volume
    if (volumeHistory.isNotEmpty) {
      final path = Path();
      final stepX = size.width / volumeHistory.length;

      for (int i = 0; i < volumeHistory.length; i++) {
        final x = i * stepX;
        final y = size.height - (volumeHistory[i] * size.height);

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      paint.color = Colors.blue;
      canvas.drawPath(path, paint);
    }

    // Dessiner les bandes de fréquences
    if (bandsHistory.isNotEmpty) {
      final bandNames = bandsHistory.first.keys.toList();
      final colors = [Colors.red, Colors.orange, Colors.yellow, Colors.green, Colors.cyan];

      for (int bandIndex = 0; bandIndex < bandNames.length && bandIndex < colors.length; bandIndex++) {
        final bandName = bandNames[bandIndex];
        final path = Path();
        final stepX = size.width / bandsHistory.length;

        for (int i = 0; i < bandsHistory.length; i++) {
          final value = bandsHistory[i][bandName] ?? 0.0;
          final x = i * stepX;
          final y = size.height - (value * size.height * 0.5) - size.height * 0.25;

          if (i == 0) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }

        paint.color = colors[bandIndex].withOpacity(0.7);
        canvas.drawPath(path, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
