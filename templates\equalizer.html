<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎵 AI Equalizer Web</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .panel {
            background: rgba(45, 45, 45, 0.9);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .panel h2 {
            margin-bottom: 20px;
            color: #4CAF50;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Contrôles audio */
        .audio-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Statut */
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.active {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }

        .status-indicator.inactive {
            background: #f44336;
        }

        /* Égaliseur */
        .equalizer {
            display: flex;
            justify-content: space-between;
            align-items: end;
            height: 300px;
            margin: 20px 0;
        }

        .eq-band {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }

        .eq-slider {
            writing-mode: bt-lr;
            -webkit-appearance: slider-vertical;
            width: 30px;
            height: 200px;
            background: linear-gradient(to top, #f44336, #ffeb3b, #4CAF50);
            outline: none;
            border-radius: 15px;
        }

        .eq-label {
            margin-top: 10px;
            font-size: 12px;
            text-align: center;
            font-weight: bold;
        }

        .eq-value {
            margin-top: 5px;
            font-size: 11px;
            color: #4CAF50;
        }

        /* Presets */
        .presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .preset-btn {
            padding: 10px;
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .preset-btn:hover {
            background: rgba(33, 150, 243, 0.4);
            transform: scale(1.05);
        }

        /* Volume maître */
        .master-volume {
            margin: 20px 0;
        }

        .volume-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right, #f44336, #ffeb3b, #4CAF50);
            outline: none;
            -webkit-appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }

        /* Détection IA */
        .ai-detection {
            text-align: center;
            padding: 20px;
            background: rgba(33, 150, 243, 0.1);
            border-radius: 10px;
            margin: 20px 0;
        }

        .genre-display {
            font-size: 2em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
        }

        .confidence-bar {
            width: 100%;
            height: 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            transition: width 0.3s ease;
        }

        /* Visualisation */
        .visualization {
            height: 200px;
            margin: 20px 0;
        }

        /* Logs */
        .logs {
            height: 150px;
            overflow-y: auto;
            background: rgba(0,0,0,0.5);
            border-radius: 8px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            opacity: 0.8;
        }

        .log-entry.info {
            color: #2196F3;
        }

        .log-entry.success {
            color: #4CAF50;
        }

        .log-entry.warning {
            color: #ff9800;
        }

        .log-entry.error {
            color: #f44336;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
            
            .equalizer {
                height: 250px;
            }
            
            .eq-slider {
                height: 150px;
            }
        }

        /* Animations */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Audio level indicator */
        .audio-level {
            width: 100%;
            height: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .audio-level-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #ffeb3b, #f44336);
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎵 AI Equalizer Web</h1>
        <p>Interface web moderne avec égaliseur intelligent</p>
    </div>

    <div class="container">
        <!-- Panel 1: Configuration et Contrôles -->
        <div class="panel">
            <h2>🎛️ Contrôles Audio</h2>
            
            <!-- Contrôles audio -->
            <div class="audio-controls">
                <button id="start-btn" class="btn btn-primary">🎧 Démarrer</button>
                <button id="stop-btn" class="btn btn-danger" disabled>⏹️ Arrêter</button>
                <button id="auto-btn" class="btn btn-warning">⚡ Auto</button>
            </div>

            <!-- Statut -->
            <div class="status">
                <div style="display: flex; align-items: center;">
                    <div id="status-indicator" class="status-indicator inactive"></div>
                    <span id="status-text">Arrêté</span>
                </div>
                <div id="audio-level-text">Niveau: 0%</div>
            </div>

            <!-- Niveau audio -->
            <div class="audio-level">
                <div id="audio-level-fill" class="audio-level-fill" style="width: 0%"></div>
            </div>

            <!-- Volume maître -->
            <div class="master-volume">
                <label>🔊 Volume Système: <span id="volume-value">100%</span></label>
                <input type="range" id="master-volume" class="volume-slider" min="0" max="1" step="0.01" value="1">
            </div>
        </div>

        <!-- Panel 2: Égaliseur -->
        <div class="panel">
            <h2>🎚️ Égaliseur 6 Bandes</h2>
            
            <!-- Presets -->
            <div class="presets">
                <button class="preset-btn" data-preset="flat">Flat</button>
                <button class="preset-btn" data-preset="rock">Rock</button>
                <button class="preset-btn" data-preset="pop">Pop</button>
                <button class="preset-btn" data-preset="electronic">Electronic</button>
                <button class="preset-btn" data-preset="classical">Classical</button>
                <button class="preset-btn" data-preset="jazz">Jazz</button>
                <button class="preset-btn" data-preset="vocal">Vocal</button>
            </div>

            <!-- Égaliseur -->
            <div class="equalizer">
                <div class="eq-band">
                    <input type="range" class="eq-slider" id="bass" min="-20" max="20" step="0.5" value="0" orient="vertical">
                    <div class="eq-label">Bass<br>60Hz</div>
                    <div class="eq-value" id="bass-value">0.0dB</div>
                </div>
                <div class="eq-band">
                    <input type="range" class="eq-slider" id="low_mid" min="-20" max="20" step="0.5" value="0" orient="vertical">
                    <div class="eq-label">Low Mid<br>170Hz</div>
                    <div class="eq-value" id="low_mid-value">0.0dB</div>
                </div>
                <div class="eq-band">
                    <input type="range" class="eq-slider" id="mid" min="-20" max="20" step="0.5" value="0" orient="vertical">
                    <div class="eq-label">Mid<br>350Hz</div>
                    <div class="eq-value" id="mid-value">0.0dB</div>
                </div>
                <div class="eq-band">
                    <input type="range" class="eq-slider" id="high_mid" min="-20" max="20" step="0.5" value="0" orient="vertical">
                    <div class="eq-label">High Mid<br>1kHz</div>
                    <div class="eq-value" id="high_mid-value">0.0dB</div>
                </div>
                <div class="eq-band">
                    <input type="range" class="eq-slider" id="treble" min="-20" max="20" step="0.5" value="0" orient="vertical">
                    <div class="eq-label">Treble<br>3.5kHz</div>
                    <div class="eq-value" id="treble-value">0.0dB</div>
                </div>
                <div class="eq-band">
                    <input type="range" class="eq-slider" id="presence" min="-20" max="20" step="0.5" value="0" orient="vertical">
                    <div class="eq-label">Presence<br>8kHz</div>
                    <div class="eq-value" id="presence-value">0.0dB</div>
                </div>
            </div>
        </div>

        <!-- Panel 3: IA et Visualisation -->
        <div class="panel">
            <h2>🤖 Détection IA</h2>
            
            <!-- Détection de genre -->
            <div class="ai-detection">
                <div class="genre-display" id="genre-display">Unknown</div>
                <div>Confiance:</div>
                <div class="confidence-bar">
                    <div class="confidence-fill" id="confidence-fill" style="width: 0%"></div>
                </div>
                <div id="confidence-text">0%</div>
            </div>

            <!-- Visualisation -->
            <div class="visualization">
                <canvas id="waveform-chart" width="400" height="200"></canvas>
            </div>

            <!-- Logs -->
            <div class="logs" id="logs">
                <div class="log-entry info">[INFO] Application web démarrée</div>
            </div>
        </div>
    </div>

    <script>
        // Initialisation SocketIO
        const socket = io();
        
        // Variables globales
        let isListening = false;
        let waveformChart = null;
        
        // Éléments DOM
        const elements = {
            startBtn: document.getElementById('start-btn'),
            stopBtn: document.getElementById('stop-btn'),
            autoBtn: document.getElementById('auto-btn'),
            statusIndicator: document.getElementById('status-indicator'),
            statusText: document.getElementById('status-text'),
            audioLevelText: document.getElementById('audio-level-text'),
            audioLevelFill: document.getElementById('audio-level-fill'),
            masterVolume: document.getElementById('master-volume'),
            volumeValue: document.getElementById('volume-value'),
            genreDisplay: document.getElementById('genre-display'),
            confidenceFill: document.getElementById('confidence-fill'),
            confidenceText: document.getElementById('confidence-text'),
            logs: document.getElementById('logs')
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            setupEventListeners();
        });

        // Configuration du graphique
        function initializeChart() {
            const ctx = document.getElementById('waveform-chart').getContext('2d');
            waveformChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 50}, (_, i) => i),
                    datasets: [{
                        label: 'Signal Audio',
                        data: Array(50).fill(0),
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { display: false },
                        y: { 
                            display: false,
                            min: -1,
                            max: 1
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    },
                    animation: false
                }
            });
        }

        // Configuration des événements
        function setupEventListeners() {
            // Boutons de contrôle
            elements.startBtn.addEventListener('click', startAudio);
            elements.stopBtn.addEventListener('click', stopAudio);
            elements.autoBtn.addEventListener('click', autoAdjust);

            // Volume maître
            elements.masterVolume.addEventListener('input', function() {
                const volume = parseFloat(this.value);
                elements.volumeValue.textContent = Math.round(volume * 100) + '%';
                
                fetch('/api/equalizer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ master_volume: volume })
                });
            });

            // Sliders d'égalisation
            document.querySelectorAll('.eq-slider').forEach(slider => {
                slider.addEventListener('input', function() {
                    const band = this.id;
                    const value = parseFloat(this.value);
                    
                    document.getElementById(band + '-value').textContent = 
                        (value >= 0 ? '+' : '') + value.toFixed(1) + 'dB';
                    
                    fetch('/api/equalizer', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ band: band, value: value })
                    });
                });
            });

            // Presets
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const preset = this.dataset.preset;
                    applyPreset(preset);
                });
            });
        }

        // Fonctions de contrôle audio
        function startAudio() {
            fetch('/api/audio/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        setAudioState(true);
                        addLog('Simulation audio démarrée', 'success');
                    }
                });
        }

        function stopAudio() {
            fetch('/api/audio/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    setAudioState(false);
                    addLog('Simulation audio arrêtée', 'info');
                });
        }

        function autoAdjust() {
            fetch('/api/equalizer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ auto_adjust: true })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateEqualizerFromData(data.bands);
                    addLog('Ajustement automatique appliqué', 'success');
                }
            });
        }

        function applyPreset(presetName) {
            fetch('/api/equalizer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ preset: presetName })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateEqualizerFromData(data.bands);
                    addLog(`Preset '${presetName}' appliqué`, 'success');
                }
            });
        }

        // Fonctions utilitaires
        function setAudioState(listening) {
            isListening = listening;
            elements.startBtn.disabled = listening;
            elements.stopBtn.disabled = !listening;
            
            elements.statusIndicator.className = 
                'status-indicator ' + (listening ? 'active' : 'inactive');
            elements.statusText.textContent = listening ? 'En écoute' : 'Arrêté';
        }

        function updateEqualizerFromData(bands) {
            Object.keys(bands).forEach(bandKey => {
                const slider = document.getElementById(bandKey);
                const valueElement = document.getElementById(bandKey + '-value');
                
                if (slider && valueElement) {
                    const value = bands[bandKey].value;
                    slider.value = value;
                    valueElement.textContent = (value >= 0 ? '+' : '') + value.toFixed(1) + 'dB';
                }
            });
        }

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            elements.logs.appendChild(logEntry);
            elements.logs.scrollTop = elements.logs.scrollHeight;
            
            // Limiter le nombre de logs
            while (elements.logs.children.length > 50) {
                elements.logs.removeChild(elements.logs.firstChild);
            }
        }

        // Événements SocketIO
        socket.on('connect', function() {
            addLog('Connecté au serveur web', 'success');
        });

        socket.on('audio_data', function(data) {
            // Mise à jour du niveau audio
            const levelPercent = Math.min(100, data.level * 100);
            elements.audioLevelFill.style.width = levelPercent + '%';
            elements.audioLevelText.textContent = `Niveau: ${levelPercent.toFixed(1)}%`;
            
            // Mise à jour du graphique
            if (waveformChart && data.waveform) {
                waveformChart.data.datasets[0].data = data.waveform;
                waveformChart.update('none');
            }
            
            // Mise à jour du genre
            elements.genreDisplay.textContent = data.genre;
            elements.confidenceFill.style.width = data.confidence + '%';
            elements.confidenceText.textContent = data.confidence.toFixed(1) + '%';
        });

        socket.on('audio_started', function() {
            setAudioState(true);
        });

        socket.on('audio_stopped', function() {
            setAudioState(false);
        });
    </script>
</body>
</html>
