name: ai_equalizer
description: AI-powered audio equalizer with real-time normalization
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  # Audio processing
  flutter_audio_capture: ^0.0.2
  audio_session: ^0.1.16
  just_audio: ^0.9.34
  # Math and signal processing
  fftea: ^1.0.0
  ml_linalg: ^13.15.0
  # AI and ML
  tflite_flutter: ^0.10.4
  # UI and state management
  provider: ^6.0.5
  flutter_colorpicker: ^1.0.3
  # System integration
  window_manager: ^0.3.7
  system_tray: ^2.0.3
  # File handling
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/models/
    - assets/images/
    - assets/audio/

  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
