#!/usr/bin/env python3
"""
AI Equalizer avec Capture Audio Système RÉELLE
Capture l'audio de Windows et applique un égaliseur en temps réel
"""

import tkinter as tk
from tkinter import ttk, messagebox
import numpy as np
import threading
import time
import json
import os
from datetime import datetime

try:
    import pyaudio
    import librosa
    from scipy import signal
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import pycaw
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    AUDIO_AVAILABLE = True
except ImportError as e:
    print(f"Modules audio non disponibles: {e}")
    print("Installation requise: pip install pyaudio librosa scipy matplotlib pycaw")
    AUDIO_AVAILABLE = False

class RealAudioEqualizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎵 AI Equalizer - Capture Audio Système RÉELLE")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # Variables d'état
        self.is_listening = False
        self.audio_data = np.array([])
        self.sample_rate = 44100
        self.chunk_size = 1024
        
        # Paramètres d'égaliseur
        self.eq_bands = {
            'Bass (60Hz)': 0.0,
            'Low Mid (170Hz)': 0.0,
            'Mid (350Hz)': 0.0,
            'High Mid (1kHz)': 0.0,
            'Treble (3.5kHz)': 0.0,
            'Presence (8kHz)': 0.0
        }
        
        # Détection de genre
        self.current_genre = "Unknown"
        self.genre_confidence = 0.0
        
        # Interface
        self.setup_ui()
        
        # Audio
        if AUDIO_AVAILABLE:
            self.setup_audio()
        else:
            self.show_installation_guide()

    def setup_ui(self):
        """Configuration de l'interface utilisateur"""
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#1e1e1e', foreground='white')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#1e1e1e', foreground='#4CAF50')
        
        # Titre principal
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(title_frame, text="🎵 AI Equalizer - Capture Audio Système RÉELLE", 
                font=('Arial', 18, 'bold'), bg='#1e1e1e', fg='white').pack()
        
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Colonne gauche - Contrôles
        left_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=2)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        self.setup_controls(left_frame)
        
        # Colonne droite - Visualisation
        right_frame = tk.Frame(main_frame, bg='#2d2d2d', relief='raised', bd=2)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        self.setup_visualization(right_frame)

    def setup_controls(self, parent):
        """Configuration des contrôles"""
        
        # Titre
        tk.Label(parent, text="🎛️ Contrôles", font=('Arial', 14, 'bold'), 
                bg='#2d2d2d', fg='white').pack(pady=10)
        
        # Bouton d'écoute
        self.listen_button = tk.Button(parent, text="🎧 Démarrer l'Écoute", 
                                      command=self.toggle_listening,
                                      font=('Arial', 12, 'bold'),
                                      bg='#4CAF50', fg='white', 
                                      width=20, height=2)
        self.listen_button.pack(pady=10)
        
        # Statut
        self.status_label = tk.Label(parent, text="❌ Arrêté", 
                                    font=('Arial', 10), bg='#2d2d2d', fg='red')
        self.status_label.pack(pady=5)
        
        # Détection de genre
        genre_frame = tk.LabelFrame(parent, text="🤖 Détection IA", 
                                   font=('Arial', 10, 'bold'),
                                   bg='#2d2d2d', fg='white')
        genre_frame.pack(fill='x', padx=10, pady=10)
        
        self.genre_label = tk.Label(genre_frame, text="Genre: Unknown", 
                                   font=('Arial', 12, 'bold'),
                                   bg='#2d2d2d', fg='#2196F3')
        self.genre_label.pack(pady=5)
        
        self.confidence_label = tk.Label(genre_frame, text="Confiance: 0%", 
                                        font=('Arial', 10),
                                        bg='#2d2d2d', fg='white')
        self.confidence_label.pack(pady=2)
        
        # Bouton auto-ajustement
        self.auto_button = tk.Button(genre_frame, text="⚡ Ajustement Auto", 
                                    command=self.auto_adjust,
                                    font=('Arial', 10, 'bold'),
                                    bg='#FF9800', fg='white', 
                                    state='disabled')
        self.auto_button.pack(pady=5)
        
        # Égaliseur
        eq_frame = tk.LabelFrame(parent, text="🎛️ Égaliseur 6 Bandes", 
                                font=('Arial', 10, 'bold'),
                                bg='#2d2d2d', fg='white')
        eq_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.eq_sliders = {}
        for i, (band, value) in enumerate(self.eq_bands.items()):
            frame = tk.Frame(eq_frame, bg='#2d2d2d')
            frame.pack(fill='x', padx=5, pady=2)
            
            tk.Label(frame, text=band, font=('Arial', 8), 
                    bg='#2d2d2d', fg='white', width=15, anchor='w').pack(side='left')
            
            slider = tk.Scale(frame, from_=-20, to=20, orient='horizontal',
                             resolution=0.5, length=150,
                             command=lambda v, b=band: self.on_eq_change(b, v),
                             bg='#2d2d2d', fg='white', highlightthickness=0)
            slider.set(value)
            slider.pack(side='left', padx=5)
            
            value_label = tk.Label(frame, text="0.0dB", font=('Arial', 8), 
                                  bg='#2d2d2d', fg='white', width=8)
            value_label.pack(side='right')
            
            self.eq_sliders[band] = {'slider': slider, 'label': value_label}
        
        # Boutons d'action
        action_frame = tk.Frame(parent, bg='#2d2d2d')
        action_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(action_frame, text="🔄 Reset", command=self.reset_eq,
                 bg='#f44336', fg='white', width=10).pack(side='left', padx=2)
        
        tk.Button(action_frame, text="💾 Sauver", command=self.save_settings,
                 bg='#2196F3', fg='white', width=10).pack(side='right', padx=2)

    def setup_visualization(self, parent):
        """Configuration de la visualisation"""
        
        tk.Label(parent, text="📊 Visualisation Audio", font=('Arial', 14, 'bold'), 
                bg='#2d2d2d', fg='white').pack(pady=10)
        
        # Graphique matplotlib
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(8, 6), 
                                                     facecolor='#2d2d2d')
        self.fig.patch.set_facecolor('#2d2d2d')
        
        # Configuration des axes
        for ax in [self.ax1, self.ax2]:
            ax.set_facecolor('#1e1e1e')
            ax.tick_params(colors='white')
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
        
        self.ax1.set_title('Signal Audio Temps Réel', color='white')
        self.ax1.set_ylabel('Amplitude', color='white')
        
        self.ax2.set_title('Spectre Fréquentiel', color='white')
        self.ax2.set_xlabel('Fréquence (Hz)', color='white')
        self.ax2.set_ylabel('Magnitude (dB)', color='white')
        
        # Canvas
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)
        
        # Logs
        log_frame = tk.LabelFrame(parent, text="📝 Logs Temps Réel", 
                                 font=('Arial', 10, 'bold'),
                                 bg='#2d2d2d', fg='white')
        log_frame.pack(fill='x', padx=10, pady=10)
        
        self.log_text = tk.Text(log_frame, height=8, bg='#1e1e1e', fg='white',
                               font=('Consolas', 9))
        scrollbar = tk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def setup_audio(self):
        """Configuration du système audio"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # Recherche du périphérique de loopback (Stereo Mix)
            self.input_device = None
            for i in range(self.audio.get_device_count()):
                info = self.audio.get_device_info_by_index(i)
                if 'stereo mix' in info['name'].lower() or 'loopback' in info['name'].lower():
                    self.input_device = i
                    break
            
            if self.input_device is None:
                # Utiliser le microphone par défaut
                self.input_device = self.audio.get_default_input_device_info()['index']
                self.log("⚠️ Stereo Mix non trouvé, utilisation du microphone")
            else:
                self.log("✅ Stereo Mix détecté pour capture système")
                
            # Configuration du volume système
            try:
                devices = AudioUtilities.GetSpeakers()
                interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
                self.volume_control = interface.QueryInterface(IAudioEndpointVolume)
                self.log("✅ Contrôle volume système activé")
            except:
                self.volume_control = None
                self.log("⚠️ Contrôle volume système non disponible")
                
        except Exception as e:
            self.log(f"❌ Erreur audio: {e}")
            self.audio = None

    def toggle_listening(self):
        """Démarre/arrête l'écoute audio"""
        if not AUDIO_AVAILABLE:
            messagebox.showerror("Erreur", "Modules audio non installés")
            return
            
        if not self.is_listening:
            self.start_listening()
        else:
            self.stop_listening()

    def start_listening(self):
        """Démarre la capture audio"""
        if not self.audio:
            self.log("❌ Système audio non initialisé")
            return
            
        try:
            self.stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.input_device,
                frames_per_buffer=self.chunk_size
            )
            
            self.is_listening = True
            self.listen_button.config(text="⏹️ Arrêter l'Écoute", bg='#f44336')
            self.status_label.config(text="✅ En Écoute", fg='green')
            self.auto_button.config(state='normal')
            
            # Démarrer le thread d'analyse
            self.audio_thread = threading.Thread(target=self.audio_processing_loop, daemon=True)
            self.audio_thread.start()
            
            self.log("🎧 Écoute audio démarrée - Lancez votre musique !")
            
        except Exception as e:
            self.log(f"❌ Erreur démarrage: {e}")

    def stop_listening(self):
        """Arrête la capture audio"""
        self.is_listening = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
            
        self.listen_button.config(text="🎧 Démarrer l'Écoute", bg='#4CAF50')
        self.status_label.config(text="❌ Arrêté", fg='red')
        self.auto_button.config(state='disabled')
        
        self.log("⏹️ Écoute audio arrêtée")

    def audio_processing_loop(self):
        """Boucle principale de traitement audio"""
        while self.is_listening:
            try:
                # Lecture des données audio
                data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.float32)
                
                # Analyse du signal
                if len(audio_data) > 0 and np.max(np.abs(audio_data)) > 0.001:
                    self.analyze_audio(audio_data)
                    self.update_visualization(audio_data)
                
                time.sleep(0.01)  # Petite pause
                
            except Exception as e:
                self.log(f"❌ Erreur traitement: {e}")
                break

    def analyze_audio(self, audio_data):
        """Analyse l'audio pour détecter le genre"""
        try:
            # Calcul du spectre
            fft = np.fft.fft(audio_data)
            freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
            magnitude = np.abs(fft)
            
            # Analyse des bandes de fréquences
            bass_energy = np.mean(magnitude[(freqs >= 20) & (freqs <= 250)])
            mid_energy = np.mean(magnitude[(freqs >= 250) & (freqs <= 4000)])
            treble_energy = np.mean(magnitude[(freqs >= 4000) & (freqs <= 20000)])
            
            # Classification simple basée sur l'énergie
            total_energy = bass_energy + mid_energy + treble_energy
            
            if total_energy > 0:
                bass_ratio = bass_energy / total_energy
                treble_ratio = treble_energy / total_energy
                
                # Détection de genre basique
                if bass_ratio > 0.4 and treble_ratio > 0.3:
                    genre = "Rock"
                    confidence = min(90, (bass_ratio + treble_ratio) * 100)
                elif bass_ratio > 0.5:
                    genre = "Electronic"
                    confidence = min(85, bass_ratio * 150)
                elif treble_ratio > 0.4:
                    genre = "Classical"
                    confidence = min(80, treble_ratio * 120)
                elif mid_energy > bass_energy and mid_energy > treble_energy:
                    genre = "Jazz"
                    confidence = min(75, (mid_energy / total_energy) * 100)
                else:
                    genre = "Pop"
                    confidence = 70
                
                # Mise à jour de l'interface
                if genre != self.current_genre:
                    self.current_genre = genre
                    self.genre_confidence = confidence
                    self.root.after(0, self.update_genre_display)
                    self.log(f"🎵 Genre détecté: {genre} ({confidence:.1f}%)")
                    
        except Exception as e:
            self.log(f"❌ Erreur analyse: {e}")

    def update_genre_display(self):
        """Met à jour l'affichage du genre"""
        self.genre_label.config(text=f"Genre: {self.current_genre}")
        self.confidence_label.config(text=f"Confiance: {self.genre_confidence:.1f}%")

    def update_visualization(self, audio_data):
        """Met à jour la visualisation"""
        try:
            # Signal temporel
            self.ax1.clear()
            self.ax1.plot(audio_data, color='#4CAF50', linewidth=1)
            self.ax1.set_title('Signal Audio Temps Réel', color='white')
            self.ax1.set_ylabel('Amplitude', color='white')
            self.ax1.set_facecolor('#1e1e1e')
            
            # Spectre fréquentiel
            self.ax2.clear()
            fft = np.fft.fft(audio_data)
            freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
            magnitude_db = 20 * np.log10(np.abs(fft) + 1e-10)
            
            # Afficher seulement les fréquences positives
            positive_freqs = freqs[:len(freqs)//2]
            positive_magnitude = magnitude_db[:len(magnitude_db)//2]
            
            self.ax2.plot(positive_freqs, positive_magnitude, color='#2196F3', linewidth=1)
            self.ax2.set_title('Spectre Fréquentiel', color='white')
            self.ax2.set_xlabel('Fréquence (Hz)', color='white')
            self.ax2.set_ylabel('Magnitude (dB)', color='white')
            self.ax2.set_xlim(0, 10000)
            self.ax2.set_facecolor('#1e1e1e')
            
            # Mise à jour du canvas
            self.root.after(0, lambda: self.canvas.draw())
            
        except Exception as e:
            pass  # Ignorer les erreurs de visualisation

    def on_eq_change(self, band, value):
        """Callback pour changement d'égaliseur"""
        value = float(value)
        self.eq_bands[band] = value
        self.eq_sliders[band]['label'].config(text=f"{value:+.1f}dB")
        
        # Application de l'égaliseur (simulation)
        self.apply_eq_to_system()
        self.log(f"🎛️ {band}: {value:+.1f}dB")

    def apply_eq_to_system(self):
        """Applique l'égaliseur au système (simulation)"""
        # Dans une vraie implémentation, on modifierait l'audio système ici
        # Pour l'instant, on simule l'effet
        pass

    def auto_adjust(self):
        """Ajustement automatique basé sur le genre détecté"""
        presets = {
            'Rock': {'Bass (60Hz)': 6, 'Low Mid (170Hz)': 2, 'Mid (350Hz)': -1, 
                    'High Mid (1kHz)': 3, 'Treble (3.5kHz)': 4, 'Presence (8kHz)': 2},
            'Electronic': {'Bass (60Hz)': 8, 'Low Mid (170Hz)': 3, 'Mid (350Hz)': -2, 
                          'High Mid (1kHz)': 1, 'Treble (3.5kHz)': 5, 'Presence (8kHz)': 3},
            'Classical': {'Bass (60Hz)': 0, 'Low Mid (170Hz)': 0, 'Mid (350Hz)': 0, 
                         'High Mid (1kHz)': 1, 'Treble (3.5kHz)': 2, 'Presence (8kHz)': 1},
            'Jazz': {'Bass (60Hz)': 1, 'Low Mid (170Hz)': 3, 'Mid (350Hz)': 2, 
                    'High Mid (1kHz)': 0, 'Treble (3.5kHz)': 1, 'Presence (8kHz)': 0},
            'Pop': {'Bass (60Hz)': 2, 'Low Mid (170Hz)': 0, 'Mid (350Hz)': 1, 
                   'High Mid (1kHz)': 2, 'Treble (3.5kHz)': 2, 'Presence (8kHz)': 1}
        }
        
        if self.current_genre in presets:
            preset = presets[self.current_genre]
            for band, value in preset.items():
                if band in self.eq_sliders:
                    self.eq_sliders[band]['slider'].set(value)
                    self.eq_bands[band] = value
                    self.eq_sliders[band]['label'].config(text=f"{value:+.1f}dB")
            
            self.log(f"⚡ Égaliseur ajusté automatiquement pour {self.current_genre}")
            messagebox.showinfo("Ajustement Auto", f"Égaliseur optimisé pour {self.current_genre}")

    def reset_eq(self):
        """Remet l'égaliseur à plat"""
        for band in self.eq_bands:
            self.eq_sliders[band]['slider'].set(0)
            self.eq_bands[band] = 0.0
            self.eq_sliders[band]['label'].config(text="0.0dB")
        
        self.log("🔄 Égaliseur remis à plat")

    def save_settings(self):
        """Sauvegarde les paramètres"""
        settings = {
            'eq_bands': self.eq_bands,
            'current_genre': self.current_genre,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            with open('equalizer_settings.json', 'w') as f:
                json.dump(settings, f, indent=2)
            self.log("💾 Paramètres sauvegardés")
            messagebox.showinfo("Sauvegarde", "Paramètres sauvegardés avec succès")
        except Exception as e:
            self.log(f"❌ Erreur sauvegarde: {e}")

    def log(self, message):
        """Ajoute un message aux logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        def update_log():
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)

    def show_installation_guide(self):
        """Affiche le guide d'installation"""
        guide = """
🔧 INSTALLATION REQUISE

Pour que l'égaliseur fonctionne avec l'audio système, installez :

1. pip install pyaudio librosa scipy matplotlib pycaw

2. Activez "Stereo Mix" dans Windows :
   - Clic droit sur l'icône son → Ouvrir les paramètres de son
   - Panneau de configuration du son → Enregistrement
   - Clic droit → Afficher les périphériques désactivés
   - Activer "Stereo Mix" ou "Mixage stéréo"

3. Redémarrez l'application

L'application capturera alors VRAIMENT l'audio de votre système !
        """
        
        messagebox.showinfo("Installation Requise", guide)
        self.log("⚠️ Modules audio manquants - Voir guide d'installation")

    def run(self):
        """Lance l'application"""
        self.log("🚀 AI Equalizer démarré")
        if AUDIO_AVAILABLE:
            self.log("✅ Modules audio disponibles")
        else:
            self.log("❌ Modules audio manquants")
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Nettoyage à la fermeture"""
        if self.is_listening:
            self.stop_listening()
        
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()
        
        self.root.destroy()

if __name__ == "__main__":
    app = RealAudioEqualizer()
    app.run()
