import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/audio_service.dart';
import 'services/ai_service.dart';
import 'services/equalizer_service.dart';
import 'services/real_audio_capture.dart';
import 'services/system_equalizer.dart';
import 'screens/main_screen.dart';
import 'providers/audio_provider.dart';
import 'providers/equalizer_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AudioProvider()),
        ChangeNotifierProvider(create: (_) => EqualizerProvider()),
        Provider(create: (_) => AudioService()),
        Provider(create: (_) => AIService()),
        Provider(create: (_) => EqualizerService()),
        Provider(create: (_) => RealAudioCapture()),
        Provider(create: (_) => SystemEqualizer()),
      ],
      child: MaterialApp(
        title: 'AI Equalizer',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          brightness: Brightness.dark,
          scaffoldBackgroundColor: const Color(0xFF1E1E1E),
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF2D2D2D),
            elevation: 0,
          ),
        ),
        home: const MainScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
