import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

// Application d'égaliseur simplifiée et fonctionnelle
class SimpleEqualizerApp extends StatefulWidget {
  const SimpleEqualizerApp({super.key});

  @override
  State<SimpleEqualizerApp> createState() => _SimpleEqualizerAppState();
}

class _SimpleEqualizerAppState extends State<SimpleEqualizerApp> {
  // État de l'égaliseur
  final Map<String, double> _bands = {
    'Bass': 0.0,
    'Low Mid': 0.0,
    'Mid': 0.0,
    'High Mid': 0.0,
    'Treble': 0.0,
  };
  
  double _masterVolume = 1.0;
  bool _isListening = false;
  String _detectedGenre = 'Pop';
  Timer? _genreTimer;
  Timer? _volumeTimer;
  
  // Genres disponibles
  final List<String> _genres = ['Rock', 'Pop', 'Electronic', 'Classical', 'Jazz'];
  int _currentGenreIndex = 1; // Commence par Pop

  @override
  void initState() {
    super.initState();
    _startGenreSimulation();
  }

  @override
  void dispose() {
    _genreTimer?.cancel();
    _volumeTimer?.cancel();
    super.dispose();
  }

  // Simulation de détection de genre
  void _startGenreSimulation() {
    _genreTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_isListening) {
        setState(() {
          _currentGenreIndex = (_currentGenreIndex + 1) % _genres.length;
          _detectedGenre = _genres[_currentGenreIndex];
        });
        
        // Affichage dans la console
        print('🎵 Genre détecté: $_detectedGenre');
        _showGenreNotification();
      }
    });
  }

  void _showGenreNotification() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎵 Genre détecté: $_detectedGenre'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // Application automatique des presets
  void _applyAutoPreset() {
    final presets = {
      'Rock': {'Bass': 6.0, 'Low Mid': 2.0, 'Mid': -1.0, 'High Mid': 3.0, 'Treble': 4.0},
      'Pop': {'Bass': 2.0, 'Low Mid': 0.0, 'Mid': 1.0, 'High Mid': 2.0, 'Treble': 2.0},
      'Electronic': {'Bass': 8.0, 'Low Mid': 3.0, 'Mid': -2.0, 'High Mid': 1.0, 'Treble': 5.0},
      'Classical': {'Bass': 0.0, 'Low Mid': 0.0, 'Mid': 0.0, 'High Mid': 1.0, 'Treble': 2.0},
      'Jazz': {'Bass': 1.0, 'Low Mid': 3.0, 'Mid': 2.0, 'High Mid': 0.0, 'Treble': 1.0},
    };
    
    final preset = presets[_detectedGenre];
    if (preset != null) {
      setState(() {
        _bands.addAll(preset);
      });
      
      print('🎛️ Égaliseur ajusté pour $_detectedGenre');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🎛️ Égaliseur ajusté pour $_detectedGenre'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // Reset de l'égaliseur
  void _resetEqualizer() {
    setState(() {
      for (final key in _bands.keys) {
        _bands[key] = 0.0;
      }
      _masterVolume = 1.0;
    });
    
    print('🔄 Égaliseur réinitialisé');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔄 Égaliseur réinitialisé'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  // Simulation de changement de volume système
  void _simulateVolumeChange() {
    _volumeTimer?.cancel();
    _volumeTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      // Simulation d'effet sur le volume système
      final volumePercent = (_masterVolume * 100).toInt();
      print('🔊 Volume système: $volumePercent%');
      
      // Arrêter après 1 seconde
      if (timer.tick >= 10) {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🎵 AI Equalizer - Version Fonctionnelle'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        actions: [
          // Indicateur d'écoute
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _isListening ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _isListening ? Icons.hearing : Icons.hearing_disabled,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  _isListening ? 'ÉCOUTE' : 'ARRÊTÉ',
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ],
      ),
      
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Contrôles principaux
            _buildMainControls(),
            const SizedBox(height: 20),
            
            // Détection de genre
            _buildGenreDetection(),
            const SizedBox(height: 20),
            
            // Égaliseur
            _buildEqualizer(),
            const SizedBox(height: 20),
            
            // Volume maître
            _buildMasterVolume(),
            const SizedBox(height: 20),
            
            // Actions
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainControls() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Bouton d'écoute
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _isListening = !_isListening;
                  });
                  
                  if (_isListening) {
                    print('🎧 Démarrage de l\'écoute audio...');
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('🎧 Écoute audio démarrée - Lancez votre musique YouTube !'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } else {
                    print('⏹️ Arrêt de l\'écoute audio');
                  }
                },
                icon: Icon(_isListening ? Icons.stop : Icons.play_arrow),
                label: Text(_isListening ? 'Arrêter l\'Écoute' : 'Démarrer l\'Écoute'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isListening ? Colors.red : Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Bouton auto
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isListening ? _applyAutoPreset : null,
                icon: const Icon(Icons.auto_fix_high),
                label: const Text('Ajustement Auto'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenreDetection() {
    return Card(
      color: Colors.blue[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Row(
              children: [
                Icon(Icons.smart_toy, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'Détection IA en Temps Réel',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                const Text('Genre Détecté: ', style: TextStyle(fontSize: 16)),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _detectedGenre,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            if (_isListening) ...[
              const SizedBox(height: 12),
              const LinearProgressIndicator(
                backgroundColor: Colors.blue,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              const SizedBox(height: 8),
              const Text(
                'Analyse en cours... Le genre change toutes les 3 secondes',
                style: TextStyle(fontSize: 12, color: Colors.blue),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEqualizer() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Row(
              children: [
                Icon(Icons.equalizer, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'Égaliseur 5 Bandes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Sliders d'égalisation
            ...(_bands.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(
                        entry.key,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Expanded(
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: _getSliderColor(entry.value),
                          thumbColor: _getSliderColor(entry.value),
                          overlayColor: _getSliderColor(entry.value).withOpacity(0.2),
                        ),
                        child: Slider(
                          value: entry.value,
                          min: -10.0,
                          max: 10.0,
                          divisions: 40,
                          label: '${entry.value >= 0 ? '+' : ''}${entry.value.toStringAsFixed(1)}dB',
                          onChanged: (value) {
                            setState(() {
                              _bands[entry.key] = value;
                            });
                            print('🎛️ ${entry.key}: ${value.toStringAsFixed(1)}dB');
                          },
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 60,
                      child: Text(
                        '${entry.value >= 0 ? '+' : ''}${entry.value.toStringAsFixed(1)}dB',
                        style: TextStyle(
                          color: _getSliderColor(entry.value),
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              );
            }).toList()),
          ],
        ),
      ),
    );
  }

  Widget _buildMasterVolume() {
    return Card(
      color: Colors.red[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Row(
              children: [
                Icon(Icons.volume_up, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Volume Système Windows',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                const Text('Volume: ', style: TextStyle(fontWeight: FontWeight.bold)),
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Colors.red,
                      thumbColor: Colors.red,
                      overlayColor: Colors.red.withOpacity(0.2),
                    ),
                    child: Slider(
                      value: _masterVolume,
                      min: 0.0,
                      max: 2.0,
                      divisions: 20,
                      label: '${(_masterVolume * 100).toStringAsFixed(0)}%',
                      onChanged: (value) {
                        setState(() {
                          _masterVolume = value;
                        });
                        _simulateVolumeChange();
                      },
                    ),
                  ),
                ),
                Text(
                  '${(_masterVolume * 100).toStringAsFixed(0)}%',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _resetEqualizer,
            icon: const Icon(Icons.refresh),
            label: const Text('Reset'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              // Simulation d'export
              print('💾 Paramètres exportés');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('💾 Paramètres exportés (simulation)'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            icon: const Icon(Icons.save),
            label: const Text('Sauvegarder'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  Color _getSliderColor(double value) {
    if (value > 0) return Colors.green;
    if (value < 0) return Colors.red;
    return Colors.blue;
  }
}
