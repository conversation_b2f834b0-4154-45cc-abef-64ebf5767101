#!/usr/bin/env python3
"""
Application Desktop Moderne - Contrôle Volume Uniforme + Égaliseur
Interface fluide et moderne avec CustomTkinter
"""

import customtkinter as ctk
import threading
import time
import math
from datetime import datetime

try:
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    VOLUME_CONTROL_AVAILABLE = True
except ImportError:
    VOLUME_CONTROL_AVAILABLE = False

# Configuration du thème moderne
ctk.set_appearance_mode("dark")  # Modes: "System", "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue", "green", "dark-blue"

class ModernVolumeApp:
    def __init__(self):
        # Fenêtre principale
        self.root = ctk.CTk()
        self.root.title("🎵 AI Equalizer - Volume Control + EQ")
        self.root.geometry("700x600")
        self.root.resizable(False, False)

        # Variables de contrôle volume
        self.volume_control = None
        self.current_volume = 50
        self.is_muted = False
        self.volume_range = None

        # Variables d'égaliseur
        self.eq_bands = {
            'bass': {'name': 'Bass', 'freq': '60Hz', 'value': 0, 'color': '#f44336'},
            'low_mid': {'name': 'Low Mid', 'freq': '170Hz', 'value': 0, 'color': '#ff9800'},
            'mid': {'name': 'Mid', 'freq': '350Hz', 'value': 0, 'color': '#ffeb3b'},
            'high_mid': {'name': 'High Mid', 'freq': '1kHz', 'value': 0, 'color': '#4caf50'},
            'treble': {'name': 'Treble', 'freq': '3.5kHz', 'value': 0, 'color': '#2196f3'},
            'presence': {'name': 'Presence', 'freq': '8kHz', 'value': 0, 'color': '#9c27b0'}
        }

        # Interface
        self.create_modern_interface()

        # Initialisation du contrôle volume
        if VOLUME_CONTROL_AVAILABLE:
            self.init_volume_control()
        else:
            self.show_error("Module pycaw non disponible")

    def create_modern_interface(self):
        """Crée l'interface moderne et fluide"""

        # Frame principal avec padding
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # Titre avec style moderne
        title_label = ctk.CTkLabel(
            main_frame,
            text="🎵 AI Equalizer",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        title_label.pack(pady=(20, 5))

        # Sous-titre
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Contrôle Volume Uniforme + Égaliseur",
            font=ctk.CTkFont(size=14),
            text_color=("gray70", "gray30")
        )
        subtitle_label.pack(pady=(0, 15))

        # Frame pour le contrôle volume
        volume_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        volume_frame.pack(fill="x", padx=20, pady=10)

        # Label volume actuel
        self.volume_label = ctk.CTkLabel(
            volume_frame,
            text="Volume: 50%",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        self.volume_label.pack(pady=(15, 8))

        # Slider de volume moderne avec contrôle uniforme
        self.volume_slider = ctk.CTkSlider(
            volume_frame,
            from_=0,
            to=100,
            number_of_steps=100,
            width=400,
            height=20,
            command=self.on_volume_change_uniform,
            button_color=("#1f538d", "#14375e"),
            button_hover_color=("#144870", "#1e5f8c"),
            progress_color=("#1f538d", "#14375e")
        )
        self.volume_slider.set(50)
        self.volume_slider.pack(pady=(8, 15))

        # Frame pour l'égaliseur
        eq_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        eq_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # Titre égaliseur
        eq_title = ctk.CTkLabel(
            eq_frame,
            text="🎚️ Égaliseur 6 Bandes",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        eq_title.pack(pady=(15, 10))

        # Container pour les sliders d'égaliseur
        eq_sliders_frame = ctk.CTkFrame(eq_frame, fg_color="transparent")
        eq_sliders_frame.pack(fill="x", padx=20, pady=10)

        # Création des sliders d'égaliseur
        self.eq_sliders = {}
        self.eq_labels = {}
        self.eq_value_labels = {}

        for i, (band_key, band_info) in enumerate(self.eq_bands.items()):
            # Frame pour chaque bande
            band_frame = ctk.CTkFrame(eq_sliders_frame, width=100, corner_radius=10)
            band_frame.pack(side="left", fill="y", padx=5, pady=5)
            band_frame.pack_propagate(False)

            # Label nom de la bande
            name_label = ctk.CTkLabel(
                band_frame,
                text=band_info['name'],
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=band_info['color']
            )
            name_label.pack(pady=(10, 2))

            # Label fréquence
            freq_label = ctk.CTkLabel(
                band_frame,
                text=band_info['freq'],
                font=ctk.CTkFont(size=10),
                text_color=("gray70", "gray30")
            )
            freq_label.pack(pady=(0, 5))

            # Slider vertical
            eq_slider = ctk.CTkSlider(
                band_frame,
                from_=20,
                to=-20,
                number_of_steps=80,
                width=20,
                height=150,
                orientation="vertical",
                command=lambda v, k=band_key: self.on_eq_change(k, v),
                button_color=band_info['color'],
                progress_color=band_info['color']
            )
            eq_slider.set(0)
            eq_slider.pack(pady=5)

            # Label valeur
            value_label = ctk.CTkLabel(
                band_frame,
                text="0dB",
                font=ctk.CTkFont(size=10),
                text_color=band_info['color']
            )
            value_label.pack(pady=(5, 10))

            # Stockage des références
            self.eq_sliders[band_key] = eq_slider
            self.eq_labels[band_key] = name_label
            self.eq_value_labels[band_key] = value_label

        # Frame pour les presets d'égaliseur
        presets_frame = ctk.CTkFrame(eq_frame, corner_radius=10)
        presets_frame.pack(fill="x", padx=20, pady=(5, 15))

        # Titre presets
        presets_title = ctk.CTkLabel(
            presets_frame,
            text="Presets:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=("gray70", "gray30")
        )
        presets_title.pack(side="left", padx=(15, 10), pady=10)

        # Boutons presets
        presets = [
            ("Flat", {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 0, "treble": 0, "presence": 0}),
            ("Rock", {"bass": 6, "low_mid": 2, "mid": -1, "high_mid": 3, "treble": 4, "presence": 2}),
            ("Pop", {"bass": 3, "low_mid": 0, "mid": 1, "high_mid": 2, "treble": 2, "presence": 1}),
            ("Electronic", {"bass": 8, "low_mid": 3, "mid": -2, "high_mid": 1, "treble": 5, "presence": 3}),
            ("Classical", {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 1, "treble": 3, "presence": 2}),
            ("Vocal", {"bass": -2, "low_mid": 1, "mid": 4, "high_mid": 3, "treble": 1, "presence": 2})
        ]

        for preset_name, preset_values in presets:
            preset_btn = ctk.CTkButton(
                presets_frame,
                text=preset_name,
                width=70,
                height=25,
                command=lambda p=preset_values: self.apply_preset(p),
                font=ctk.CTkFont(size=10),
                fg_color=("#2196f3", "#1976d2"),
                hover_color=("#64b5f6", "#2196f3")
            )
            preset_btn.pack(side="left", padx=2, pady=10)

        # Frame pour les boutons de contrôle
        buttons_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        # Boutons de contrôle rapide
        buttons_container = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        buttons_container.pack(pady=20)

        # Première ligne de boutons
        button_frame1 = ctk.CTkFrame(buttons_container, fg_color="transparent")
        button_frame1.pack(pady=5)

        self.mute_button = ctk.CTkButton(
            button_frame1,
            text="🔇 Muet",
            width=100,
            height=35,
            command=self.toggle_mute,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#d32f2f", "#b71c1c"),
            hover_color=("#f44336", "#d32f2f")
        )
        self.mute_button.pack(side="left", padx=5)

        self.vol_25_button = ctk.CTkButton(
            button_frame1,
            text="🔉 25%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(25),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#ff9800", "#f57c00"),
            hover_color=("#ffb74d", "#ff9800")
        )
        self.vol_25_button.pack(side="left", padx=5)

        self.vol_50_button = ctk.CTkButton(
            button_frame1,
            text="🔊 50%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(50),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#2196f3", "#1976d2"),
            hover_color=("#64b5f6", "#2196f3")
        )
        self.vol_50_button.pack(side="left", padx=5)

        # Deuxième ligne de boutons
        button_frame2 = ctk.CTkFrame(buttons_container, fg_color="transparent")
        button_frame2.pack(pady=5)

        self.vol_75_button = ctk.CTkButton(
            button_frame2,
            text="🔊 75%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(75),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#4caf50", "#388e3c"),
            hover_color=("#81c784", "#4caf50")
        )
        self.vol_75_button.pack(side="left", padx=5)

        self.vol_100_button = ctk.CTkButton(
            button_frame2,
            text="🔊 100%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(100),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#9c27b0", "#7b1fa2"),
            hover_color=("#ba68c8", "#9c27b0")
        )
        self.vol_100_button.pack(side="left", padx=5)

        self.refresh_button = ctk.CTkButton(
            button_frame2,
            text="🔄 Actualiser",
            width=100,
            height=35,
            command=self.refresh_volume,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#607d8b", "#455a64"),
            hover_color=("#90a4ae", "#607d8b")
        )
        self.refresh_button.pack(side="left", padx=5)

        # Frame pour le statut
        status_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        status_frame.pack(fill="x", padx=30, pady=(10, 20))

        # Label de statut
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Initialisation...",
            font=ctk.CTkFont(size=14),
            text_color=("gray70", "gray30")
        )
        self.status_label.pack(pady=15)

    def init_volume_control(self):
        """Initialise le contrôle du volume système"""
        try:
            # Obtenir le périphérique audio par défaut
            devices = AudioUtilities.GetSpeakers()
            interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
            self.volume_control = interface.QueryInterface(IAudioEndpointVolume)

            # Obtenir les informations sur le volume
            self.volume_range = self.volume_control.GetVolumeRange()

            # Lire le volume actuel
            current_vol_db = self.volume_control.GetMasterVolumeLevel()
            volume_percent = self.db_to_percent(current_vol_db)

            # Mettre à jour l'interface
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)
            self.update_volume_display(volume_percent)

            # Statut de succès
            self.status_label.configure(
                text="✅ Contrôle volume initialisé avec succès",
                text_color=("#4caf50", "#388e3c")
            )

        except Exception as e:
            self.show_error(f"Erreur initialisation: {e}")

    def db_to_percent(self, db_value):
        """Convertit une valeur dB en pourcentage"""
        if not self.volume_range:
            return 50

        min_vol, max_vol, _ = self.volume_range
        percent = ((db_value - min_vol) / (max_vol - min_vol)) * 100
        return max(0, min(100, int(percent)))

    def percent_to_db(self, percent):
        """Convertit un pourcentage en valeur dB"""
        if not self.volume_range:
            return -20.0

        min_vol, max_vol, _ = self.volume_range
        db_value = min_vol + (percent / 100.0) * (max_vol - min_vol)
        return db_value

    def on_volume_change_uniform(self, value):
        """Callback pour contrôle volume uniforme (courbe logarithmique)"""
        # Conversion linéaire vers logarithmique pour un contrôle plus uniforme
        linear_value = float(value)

        # Courbe logarithmique pour perception uniforme
        if linear_value == 0:
            uniform_value = 0
        else:
            # Formule logarithmique pour perception audio uniforme
            uniform_value = (math.pow(linear_value / 100.0, 0.5)) * 100

        volume_percent = int(uniform_value)
        self.set_volume_percent(volume_percent)

        # Mise à jour du label avec la valeur perçue
        self.volume_label.configure(text=f"Volume: {int(linear_value)}%")

    def on_eq_change(self, band_key, value):
        """Callback pour changement d'égaliseur"""
        eq_value = float(value)
        self.eq_bands[band_key]['value'] = eq_value

        # Mise à jour du label
        if eq_value >= 0:
            self.eq_value_labels[band_key].configure(text=f"+{eq_value:.1f}dB")
        else:
            self.eq_value_labels[band_key].configure(text=f"{eq_value:.1f}dB")

        # Application de l'égalisation (simulation pour l'instant)
        self.apply_eq_band(band_key, eq_value)

    def apply_eq_band(self, band_key, value):
        """Applique l'égalisation pour une bande (simulation)"""
        band_info = self.eq_bands[band_key]

        # Mise à jour du statut
        if value == 0:
            status_text = f"🎚️ {band_info['name']} remis à plat"
        elif value > 0:
            status_text = f"🎚️ {band_info['name']} boost +{value:.1f}dB"
        else:
            status_text = f"🎚️ {band_info['name']} cut {value:.1f}dB"

        self.status_label.configure(
            text=status_text,
            text_color=band_info['color']
        )

    def apply_preset(self, preset_values):
        """Applique un preset d'égaliseur"""
        for band_key, value in preset_values.items():
            if band_key in self.eq_sliders:
                # Mise à jour du slider
                self.eq_sliders[band_key].set(value)

                # Mise à jour des valeurs internes
                self.eq_bands[band_key]['value'] = value

                # Mise à jour du label
                if value >= 0:
                    self.eq_value_labels[band_key].configure(text=f"+{value:.1f}dB")
                else:
                    self.eq_value_labels[band_key].configure(text=f"{value:.1f}dB")

        # Mise à jour du statut
        self.status_label.configure(
            text="⚡ Preset d'égaliseur appliqué",
            text_color=("#4caf50", "#388e3c")
        )

    def set_volume_percent(self, volume_percent):
        """Définit le volume système en pourcentage"""
        if not self.volume_control:
            self.show_error("Contrôle volume non disponible")
            return

        try:
            # Conversion pourcentage vers dB
            volume_db = self.percent_to_db(volume_percent)

            # Application du volume
            self.volume_control.SetMasterVolumeLevel(volume_db, None)

            # Mise à jour interface
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)
            self.update_volume_display(volume_percent)

            # Statut de succès
            self.status_label.configure(
                text=f"✅ Volume défini à {volume_percent}%",
                text_color=("#4caf50", "#388e3c")
            )

        except Exception as e:
            self.show_error(f"Erreur définition volume: {e}")

    def toggle_mute(self):
        """Active/désactive le mode muet"""
        if not self.volume_control:
            self.show_error("Contrôle volume non disponible")
            return

        try:
            # Lire l'état actuel du muet
            current_mute = self.volume_control.GetMute()

            # Basculer le mode muet
            new_mute = not current_mute
            self.volume_control.SetMute(new_mute, None)

            if new_mute:
                self.volume_label.configure(text="Volume: MUET", text_color=("#d32f2f", "#b71c1c"))
                self.mute_button.configure(text="🔊 Rétablir")
                self.status_label.configure(
                    text="🔇 Audio mis en sourdine",
                    text_color=("#d32f2f", "#b71c1c")
                )
            else:
                self.update_volume_display(self.current_volume)
                self.mute_button.configure(text="🔇 Muet")
                self.status_label.configure(
                    text="🔊 Audio rétabli",
                    text_color=("#4caf50", "#388e3c")
                )

        except Exception as e:
            self.show_error(f"Erreur mode muet: {e}")

    def refresh_volume(self):
        """Actualise le volume depuis le système"""
        if not self.volume_control:
            self.show_error("Contrôle volume non disponible")
            return

        try:
            # Lire le volume actuel
            current_vol_db = self.volume_control.GetMasterVolumeLevel()
            volume_percent = self.db_to_percent(current_vol_db)

            # Lire l'état muet
            is_muted = self.volume_control.GetMute()

            # Mettre à jour l'interface
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)

            if is_muted:
                self.volume_label.configure(text="Volume: MUET", text_color=("#d32f2f", "#b71c1c"))
                self.mute_button.configure(text="🔊 Rétablir")
            else:
                self.update_volume_display(volume_percent)
                self.mute_button.configure(text="🔇 Muet")

            self.status_label.configure(
                text=f"🔄 Volume actualisé: {volume_percent}%",
                text_color=("#2196f3", "#1976d2")
            )

        except Exception as e:
            self.show_error(f"Erreur actualisation: {e}")

    def update_volume_display(self, volume_percent):
        """Met à jour l'affichage du volume"""
        self.volume_label.configure(
            text=f"Volume: {volume_percent}%",
            text_color=("#1f538d", "#14375e")
        )

    def show_error(self, message):
        """Affiche un message d'erreur"""
        self.status_label.configure(
            text=f"❌ {message}",
            text_color=("#d32f2f", "#b71c1c")
        )

    def run(self):
        """Lance l'application"""
        # Centrer la fenêtre
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # Démarrer l'application
        self.root.mainloop()

if __name__ == "__main__":
    app = ModernVolumeApp()
    app.run()
