#!/usr/bin/env python3
"""
Application Desktop Moderne - Contrôle Volume Uniforme
Interface fluide et moderne avec CustomTkinter
"""

import customtkinter as ctk
import threading
import time
from datetime import datetime

try:
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    VOLUME_CONTROL_AVAILABLE = True
except ImportError:
    VOLUME_CONTROL_AVAILABLE = False

# Configuration du thème moderne
ctk.set_appearance_mode("dark")  # Modes: "System", "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue", "green", "dark-blue"

class ModernVolumeApp:
    def __init__(self):
        # Fenêtre principale
        self.root = ctk.CTk()
        self.root.title("🎵 AI Equalizer - Volume Control")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Variables
        self.volume_control = None
        self.current_volume = 50
        self.is_muted = False
        self.volume_range = None
        
        # Interface
        self.create_modern_interface()
        
        # Initialisation du contrôle volume
        if VOLUME_CONTROL_AVAILABLE:
            self.init_volume_control()
        else:
            self.show_error("Module pycaw non disponible")

    def create_modern_interface(self):
        """Crée l'interface moderne et fluide"""
        
        # Frame principal avec padding
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Titre avec style moderne
        title_label = ctk.CTkLabel(
            main_frame,
            text="🎵 AI Equalizer",
            font=ctk.CTkFont(size=32, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        title_label.pack(pady=(30, 10))
        
        # Sous-titre
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Contrôle Volume Uniforme",
            font=ctk.CTkFont(size=16),
            text_color=("gray70", "gray30")
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Frame pour le contrôle volume
        volume_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        volume_frame.pack(fill="x", padx=30, pady=20)
        
        # Label volume actuel
        self.volume_label = ctk.CTkLabel(
            volume_frame,
            text="Volume: 50%",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=("#1f538d", "#14375e")
        )
        self.volume_label.pack(pady=(20, 10))
        
        # Slider de volume moderne
        self.volume_slider = ctk.CTkSlider(
            volume_frame,
            from_=0,
            to=100,
            number_of_steps=100,
            width=350,
            height=20,
            command=self.on_volume_change,
            button_color=("#1f538d", "#14375e"),
            button_hover_color=("#144870", "#1e5f8c"),
            progress_color=("#1f538d", "#14375e")
        )
        self.volume_slider.set(50)
        self.volume_slider.pack(pady=(10, 20))
        
        # Frame pour les boutons
        buttons_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        buttons_frame.pack(fill="x", padx=30, pady=10)
        
        # Boutons de contrôle rapide
        buttons_container = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        buttons_container.pack(pady=20)
        
        # Première ligne de boutons
        button_frame1 = ctk.CTkFrame(buttons_container, fg_color="transparent")
        button_frame1.pack(pady=5)
        
        self.mute_button = ctk.CTkButton(
            button_frame1,
            text="🔇 Muet",
            width=100,
            height=35,
            command=self.toggle_mute,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#d32f2f", "#b71c1c"),
            hover_color=("#f44336", "#d32f2f")
        )
        self.mute_button.pack(side="left", padx=5)
        
        self.vol_25_button = ctk.CTkButton(
            button_frame1,
            text="🔉 25%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(25),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#ff9800", "#f57c00"),
            hover_color=("#ffb74d", "#ff9800")
        )
        self.vol_25_button.pack(side="left", padx=5)
        
        self.vol_50_button = ctk.CTkButton(
            button_frame1,
            text="🔊 50%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(50),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#2196f3", "#1976d2"),
            hover_color=("#64b5f6", "#2196f3")
        )
        self.vol_50_button.pack(side="left", padx=5)
        
        # Deuxième ligne de boutons
        button_frame2 = ctk.CTkFrame(buttons_container, fg_color="transparent")
        button_frame2.pack(pady=5)
        
        self.vol_75_button = ctk.CTkButton(
            button_frame2,
            text="🔊 75%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(75),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#4caf50", "#388e3c"),
            hover_color=("#81c784", "#4caf50")
        )
        self.vol_75_button.pack(side="left", padx=5)
        
        self.vol_100_button = ctk.CTkButton(
            button_frame2,
            text="🔊 100%",
            width=100,
            height=35,
            command=lambda: self.set_volume_percent(100),
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#9c27b0", "#7b1fa2"),
            hover_color=("#ba68c8", "#9c27b0")
        )
        self.vol_100_button.pack(side="left", padx=5)
        
        self.refresh_button = ctk.CTkButton(
            button_frame2,
            text="🔄 Actualiser",
            width=100,
            height=35,
            command=self.refresh_volume,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#607d8b", "#455a64"),
            hover_color=("#90a4ae", "#607d8b")
        )
        self.refresh_button.pack(side="left", padx=5)
        
        # Frame pour le statut
        status_frame = ctk.CTkFrame(main_frame, corner_radius=15)
        status_frame.pack(fill="x", padx=30, pady=(10, 20))
        
        # Label de statut
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Initialisation...",
            font=ctk.CTkFont(size=14),
            text_color=("gray70", "gray30")
        )
        self.status_label.pack(pady=15)

    def init_volume_control(self):
        """Initialise le contrôle du volume système"""
        try:
            # Obtenir le périphérique audio par défaut
            devices = AudioUtilities.GetSpeakers()
            interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
            self.volume_control = interface.QueryInterface(IAudioEndpointVolume)
            
            # Obtenir les informations sur le volume
            self.volume_range = self.volume_control.GetVolumeRange()
            
            # Lire le volume actuel
            current_vol_db = self.volume_control.GetMasterVolumeLevel()
            volume_percent = self.db_to_percent(current_vol_db)
            
            # Mettre à jour l'interface
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)
            self.update_volume_display(volume_percent)
            
            # Statut de succès
            self.status_label.configure(
                text="✅ Contrôle volume initialisé avec succès",
                text_color=("#4caf50", "#388e3c")
            )
            
        except Exception as e:
            self.show_error(f"Erreur initialisation: {e}")

    def db_to_percent(self, db_value):
        """Convertit une valeur dB en pourcentage"""
        if not self.volume_range:
            return 50
        
        min_vol, max_vol, _ = self.volume_range
        percent = ((db_value - min_vol) / (max_vol - min_vol)) * 100
        return max(0, min(100, int(percent)))

    def percent_to_db(self, percent):
        """Convertit un pourcentage en valeur dB"""
        if not self.volume_range:
            return -20.0
        
        min_vol, max_vol, _ = self.volume_range
        db_value = min_vol + (percent / 100.0) * (max_vol - min_vol)
        return db_value

    def on_volume_change(self, value):
        """Callback quand le slider change"""
        volume_percent = int(value)
        self.set_volume_percent(volume_percent)

    def set_volume_percent(self, volume_percent):
        """Définit le volume système en pourcentage"""
        if not self.volume_control:
            self.show_error("Contrôle volume non disponible")
            return
        
        try:
            # Conversion pourcentage vers dB
            volume_db = self.percent_to_db(volume_percent)
            
            # Application du volume
            self.volume_control.SetMasterVolumeLevel(volume_db, None)
            
            # Mise à jour interface
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)
            self.update_volume_display(volume_percent)
            
            # Statut de succès
            self.status_label.configure(
                text=f"✅ Volume défini à {volume_percent}%",
                text_color=("#4caf50", "#388e3c")
            )
            
        except Exception as e:
            self.show_error(f"Erreur définition volume: {e}")

    def toggle_mute(self):
        """Active/désactive le mode muet"""
        if not self.volume_control:
            self.show_error("Contrôle volume non disponible")
            return
        
        try:
            # Lire l'état actuel du muet
            current_mute = self.volume_control.GetMute()
            
            # Basculer le mode muet
            new_mute = not current_mute
            self.volume_control.SetMute(new_mute, None)
            
            if new_mute:
                self.volume_label.configure(text="Volume: MUET", text_color=("#d32f2f", "#b71c1c"))
                self.mute_button.configure(text="🔊 Rétablir")
                self.status_label.configure(
                    text="🔇 Audio mis en sourdine",
                    text_color=("#d32f2f", "#b71c1c")
                )
            else:
                self.update_volume_display(self.current_volume)
                self.mute_button.configure(text="🔇 Muet")
                self.status_label.configure(
                    text="🔊 Audio rétabli",
                    text_color=("#4caf50", "#388e3c")
                )
                
        except Exception as e:
            self.show_error(f"Erreur mode muet: {e}")

    def refresh_volume(self):
        """Actualise le volume depuis le système"""
        if not self.volume_control:
            self.show_error("Contrôle volume non disponible")
            return
        
        try:
            # Lire le volume actuel
            current_vol_db = self.volume_control.GetMasterVolumeLevel()
            volume_percent = self.db_to_percent(current_vol_db)
            
            # Lire l'état muet
            is_muted = self.volume_control.GetMute()
            
            # Mettre à jour l'interface
            self.current_volume = volume_percent
            self.volume_slider.set(volume_percent)
            
            if is_muted:
                self.volume_label.configure(text="Volume: MUET", text_color=("#d32f2f", "#b71c1c"))
                self.mute_button.configure(text="🔊 Rétablir")
            else:
                self.update_volume_display(volume_percent)
                self.mute_button.configure(text="🔇 Muet")
            
            self.status_label.configure(
                text=f"🔄 Volume actualisé: {volume_percent}%",
                text_color=("#2196f3", "#1976d2")
            )
            
        except Exception as e:
            self.show_error(f"Erreur actualisation: {e}")

    def update_volume_display(self, volume_percent):
        """Met à jour l'affichage du volume"""
        self.volume_label.configure(
            text=f"Volume: {volume_percent}%",
            text_color=("#1f538d", "#14375e")
        )

    def show_error(self, message):
        """Affiche un message d'erreur"""
        self.status_label.configure(
            text=f"❌ {message}",
            text_color=("#d32f2f", "#b71c1c")
        )

    def run(self):
        """Lance l'application"""
        # Centrer la fenêtre
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        # Démarrer l'application
        self.root.mainloop()

if __name__ == "__main__":
    app = ModernVolumeApp()
    app.run()
