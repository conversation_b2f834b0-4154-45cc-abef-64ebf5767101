# AI Equalizer

Une application d'égaliseur intelligent avec IA pour desktop et mobile, développée en Flutter.

## Fonctionnalités

### 🎵 Égaliseur Audio
- **6 bandes de fréquences** : Bass, Low Mid, Mid, High Mid, Treble, Presence
- **Gain maître** : Contrôle du volume global
- **Presets intégrés** : Rock, Pop, Classical, Jazz, Electronic, Vocal
- **Interface intuitive** avec sliders verticaux

### 🤖 Intelligence Artificielle
- **Détection automatique de genre musical** (gratuite)
- **Classification en temps réel** : Rock, Pop, Classical, Jazz, Electronic, Hip-Hop, Country, Blues, Reggae
- **Application automatique des presets** selon le genre détecté
- **Analyse de confiance** pour chaque genre

### 🔊 Normalisation Audio Avancée
- **Algorithmes mathématiques** pour maintenir un volume constant
- **LUFS (Loudness Units relative to Full Scale)** pour la mesure de loudness
- **AGC (Automatic Gain Control)** avec lissage temporel
- **Compression dynamique** pour éviter les pics et amplifier les parties faibles

### 📊 Visualisation
- **Visualiseur audio en temps réel**
- **Historique des bandes de fréquences**
- **Métriques détaillées** : Volume, Gain, LUFS, Statistiques
- **Interface compacte** pour fonctionnement en arrière-plan

## Algorithmes Implémentés

### Normalisation du Volume
```
gain(t) = target_level / current_level(t)
gain_smooth(t) = α × gain(t-1) + (1-α) × target_gain(t)
```

### Calcul LUFS
```
LUFS = -0.691 + 10 × log10(loudness)
```

### Compression Dynamique
```
if |input| > threshold:
    output = threshold + (|input| - threshold) / ratio
```

### Filtres IIR pour Égalisation
```
y(n) = b0×x(n) + b1×x(n-1) + b2×x(n-2) - a1×y(n-1) - a2×y(n-2)
```

## Installation et Compilation

### Prérequis
1. **Flutter SDK** (version 3.0+)
2. **Visual Studio 2019/2022** avec C++ build tools (pour Windows)
3. **Git**

### Installation Flutter
1. Téléchargez Flutter depuis [flutter.dev](https://flutter.dev/docs/get-started/install/windows)
2. Extrayez l'archive dans `C:\flutter`
3. Ajoutez `C:\flutter\bin` au PATH système
4. Exécutez `flutter doctor` pour vérifier l'installation

### Compilation

#### Méthode 1 : Script automatique
```bash
# Double-cliquez sur build.bat ou exécutez :
build.bat
```

#### Méthode 2 : Commandes manuelles
```bash
# Installer les dépendances
flutter pub get

# Compiler pour Windows
flutter build windows --release

# L'exécutable sera dans : build/windows/runner/Release/ai_equalizer.exe
```

#### Méthode 3 : Mode développement
```bash
# Lancer en mode debug
flutter run -d windows
```

## Utilisation

### Interface Principale
1. **Onglet Égaliseur** : Ajustez les bandes de fréquences manuellement
2. **Onglet Visualiseur** : Observez l'audio en temps réel
3. **Onglet IA** : Activez la détection automatique de genre
4. **Onglet Contrôles** : Paramètres avancés de normalisation

### Mode Automatique
1. Activez l'**Analyse IA** dans l'onglet IA
2. Activez l'**Auto-ajustement** 
3. L'application détectera automatiquement le genre et appliquera le preset approprié

### Mode Compact
- Cliquez sur l'icône de réduction pour passer en mode compact
- L'application continue de fonctionner en arrière-plan
- Visualisation simplifiée des métriques principales

## Configuration

### Paramètres de Normalisation
- **LUFS Cible** : -23 LUFS (standard broadcast)
- **Lissage (Alpha)** : 0.95 (réactivité du gain)
- **Gain Maximum** : +6 dB (limite haute)
- **Gain Minimum** : -20 dB (limite basse)

### Presets Disponibles
- **Soft** : Normalisation douce pour écoute relaxante
- **Normal** : Paramètres équilibrés (défaut)
- **Aggressive** : Normalisation forte pour environnements bruyants
- **Broadcast** : Conforme aux standards de diffusion

## Architecture Technique

### Services
- **AudioService** : Capture et traitement audio en temps réel
- **AIService** : Classification de genre musical (IA gratuite)
- **EqualizerService** : Filtres IIR et égalisation

### Providers (State Management)
- **AudioProvider** : Gestion de l'état audio et normalisation
- **EqualizerProvider** : Gestion de l'égaliseur et IA

### Widgets
- **EqualizerWidget** : Interface des bandes d'égalisation
- **AudioVisualizer** : Visualisation temps réel
- **AIPanel** : Contrôles et affichage IA
- **ControlPanel** : Paramètres avancés

## Développement Futur

### Fonctionnalités Prévues
- [ ] **Capture audio système réelle** (actuellement simulée)
- [ ] **Modèles IA plus avancés** (TensorFlow Lite)
- [ ] **Sauvegarde/chargement** de presets personnalisés
- [ ] **Version Android** complète
- [ ] **Service d'arrière-plan Windows**
- [ ] **Intégration avec lecteurs audio populaires**

### Améliorations Techniques
- [ ] **FFT réelle** pour analyse spectrale
- [ ] **Filtres audio natifs** (WASAPI/DirectSound)
- [ ] **Optimisations performances**
- [ ] **Tests unitaires complets**

## Dépannage

### Problèmes Courants

**Flutter non reconnu**
```bash
# Vérifiez l'installation
flutter doctor

# Ajoutez Flutter au PATH si nécessaire
```

**Erreur de compilation Windows**
```bash
# Installez Visual Studio Build Tools
# Ou utilisez Visual Studio Community 2022
```

**Dépendances manquantes**
```bash
flutter clean
flutter pub get
```

## Contribution

Les contributions sont les bienvenues ! Veuillez :
1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## Contact

Pour toute question ou suggestion, n'hésitez pas à ouvrir une issue sur GitHub.

---

**Note** : Cette version utilise des simulations pour l'audio et l'IA. Pour une utilisation en production, il faudra intégrer de vraies bibliothèques de capture audio et des modèles IA entraînés.
