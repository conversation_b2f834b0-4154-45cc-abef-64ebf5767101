@echo off
echo ========================================
echo    INSTALLATION VB-AUDIO VIRTUAL CABLE
echo ========================================
echo.

echo [1/4] Telechargement de VB-Audio Virtual Cable...
echo.

:: C<PERSON>er le dossier de téléchargement
if not exist "downloads" mkdir downloads

:: Télécharger VB-Audio Virtual Cable (gratuit)
echo Telechargement en cours...
powershell -Command "& {Invoke-WebRequest -Uri 'https://download.vb-audio.com/Download_CABLE/VBCABLE_Driver_Pack43.zip' -OutFile 'downloads\VBCABLE_Driver_Pack43.zip'}"

if not exist "downloads\VBCABLE_Driver_Pack43.zip" (
    echo ERREUR: Telechargement echoue
    echo Telechargez manuellement depuis: https://vb-audio.com/Cable/
    pause
    exit /b 1
)

echo ✅ Telechargement termine

echo.
echo [2/4] Extraction des fichiers...
powershell -Command "& {Expand-Archive -Path 'downloads\VBCABLE_Driver_Pack43.zip' -DestinationPath 'downloads\vbcable' -Force}"

echo ✅ Extraction terminee

echo.
echo [3/4] Installation du driver VB-Audio...
echo.
echo ⚠️ IMPORTANT: L'installation du driver necessite les droits administrateur
echo ⚠️ Cliquez "Oui" quand Windows demande l'autorisation
echo.
pause

:: Installer le driver (nécessite les droits admin)
cd downloads\vbcable
VBCABLE_Setup_x64.exe

echo.
echo [4/4] Verification de l'installation...
echo.
echo ✅ VB-Audio Virtual Cable installe !
echo.
echo 📋 CONFIGURATION REQUISE:
echo.
echo 1. Redemarrez votre ordinateur
echo 2. Apres redemarrage, vous verrez dans Windows:
echo    - "CABLE Input" (peripherique de lecture)
echo    - "CABLE Output" (peripherique d'enregistrement)
echo.
echo 3. Configuration pour l'AI Equalizer:
echo    - Peripherique de lecture par defaut: CABLE Input
echo    - L'application capturera via CABLE Output
echo    - L'application redirigera vers vos vrais haut-parleurs
echo.
echo 🎵 Votre AI Equalizer pourra alors intercepter TOUT l'audio !
echo.
echo Redemarrez maintenant ? (O/N)
set /p restart="Votre choix: "

if /i "%restart%"=="O" (
    echo Redemarrage en cours...
    shutdown /r /t 10 /c "Redemarrage pour VB-Audio Virtual Cable"
) else (
    echo Redemarrez manuellement pour terminer l'installation
)

pause
