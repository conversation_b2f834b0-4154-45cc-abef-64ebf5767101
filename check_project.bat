@echo off
echo Checking AI Equalizer Project Structure...
echo.

REM Check main files
echo [1/10] Checking main configuration files...
if exist "pubspec.yaml" (
    echo ✓ pubspec.yaml found
) else (
    echo ✗ pubspec.yaml missing
)

if exist "lib\main.dart" (
    echo ✓ lib\main.dart found
) else (
    echo ✗ lib\main.dart missing
)

if exist "README.md" (
    echo ✓ README.md found
) else (
    echo ✗ README.md missing
)

echo.
echo [2/10] Checking service files...
if exist "lib\services\audio_service.dart" (
    echo ✓ AudioService found
) else (
    echo ✗ AudioService missing
)

if exist "lib\services\ai_service.dart" (
    echo ✓ AIService found
) else (
    echo ✗ AIService missing
)

if exist "lib\services\equalizer_service.dart" (
    echo ✓ EqualizerService found
) else (
    echo ✗ EqualizerService missing
)

echo.
echo [3/10] Checking provider files...
if exist "lib\providers\audio_provider.dart" (
    echo ✓ AudioProvider found
) else (
    echo ✗ AudioProvider missing
)

if exist "lib\providers\equalizer_provider.dart" (
    echo ✓ EqualizerProvider found
) else (
    echo ✗ EqualizerProvider missing
)

echo.
echo [4/10] Checking screen files...
if exist "lib\screens\main_screen.dart" (
    echo ✓ MainScreen found
) else (
    echo ✗ MainScreen missing
)

echo.
echo [5/10] Checking widget files...
if exist "lib\widgets\equalizer_widget.dart" (
    echo ✓ EqualizerWidget found
) else (
    echo ✗ EqualizerWidget missing
)

if exist "lib\widgets\audio_visualizer.dart" (
    echo ✓ AudioVisualizer found
) else (
    echo ✗ AudioVisualizer missing
)

if exist "lib\widgets\ai_panel.dart" (
    echo ✓ AIPanel found
) else (
    echo ✗ AIPanel missing
)

if exist "lib\widgets\control_panel.dart" (
    echo ✓ ControlPanel found
) else (
    echo ✗ ControlPanel missing
)

echo.
echo [6/10] Checking Windows configuration...
if exist "windows\CMakeLists.txt" (
    echo ✓ Windows CMakeLists.txt found
) else (
    echo ✗ Windows CMakeLists.txt missing
)

if exist "windows\runner\main.cpp" (
    echo ✓ Windows main.cpp found
) else (
    echo ✗ Windows main.cpp missing
)

echo.
echo [7/10] Checking test files...
if exist "test\widget_test.dart" (
    echo ✓ Widget tests found
) else (
    echo ✗ Widget tests missing
)

echo.
echo [8/10] Checking build scripts...
if exist "build.bat" (
    echo ✓ Build script found
) else (
    echo ✗ Build script missing
)

if exist "run_debug.bat" (
    echo ✓ Debug script found
) else (
    echo ✗ Debug script missing
)

if exist "test_app.bat" (
    echo ✓ Test script found
) else (
    echo ✗ Test script missing
)

echo.
echo [9/10] Checking documentation...
if exist "INSTALLATION.md" (
    echo ✓ Installation guide found
) else (
    echo ✗ Installation guide missing
)

if exist "analysis_options.yaml" (
    echo ✓ Analysis options found
) else (
    echo ✗ Analysis options missing
)

echo.
echo [10/10] Checking assets structure...
if exist "assets\README.md" (
    echo ✓ Assets directory found
) else (
    echo ✗ Assets directory missing
)

echo.
echo ================================================
echo Project Structure Check Complete!
echo ================================================
echo.
echo Next steps:
echo 1. Install Flutter: https://flutter.dev/docs/get-started/install/windows
echo 2. Run: flutter pub get
echo 3. Test: run_debug.bat
echo 4. Build: build.bat
echo.
echo For detailed instructions, see INSTALLATION.md
echo.

pause
