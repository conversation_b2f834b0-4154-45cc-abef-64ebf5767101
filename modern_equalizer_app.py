#!/usr/bin/env python3
"""
AI Equalizer Moderne - Interface Web Complète
Avec sélection de périphériques audio et contrôle système réel
"""

import asyncio
import json
import os
import threading
import time
from datetime import datetime
from pathlib import Path

try:
    import numpy as np
    import pyaudio
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import SocketIO, emit
    import pycaw
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume, ISimpleAudioVolume
    from comtypes import CLSCTX_ALL
    AUDIO_AVAILABLE = True
    print("✅ Tous les modules audio sont disponibles")
except ImportError as e:
    print(f"❌ Modules manquants: {e}")
    print("Installation: pip install flask flask-socketio pyaudio numpy pycaw")
    AUDIO_AVAILABLE = False

class ModernEqualizerApp:
    def __init__(self):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'equalizer_secret_key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Variables d'état
        self.is_listening = False
        self.sample_rate = 44100
        self.chunk_size = 1024
        self.audio_devices = []
        self.output_devices = []
        self.selected_input_device = None
        self.selected_output_device = None
        
        # Paramètres d'égaliseur
        self.eq_bands = {
            'bass': {'name': 'Bass', 'freq': '60Hz', 'value': 0.0},
            'low_mid': {'name': 'Low Mid', 'freq': '170Hz', 'value': 0.0},
            'mid': {'name': 'Mid', 'freq': '350Hz', 'value': 0.0},
            'high_mid': {'name': 'High Mid', 'freq': '1kHz', 'value': 0.0},
            'treble': {'name': 'Treble', 'freq': '3.5kHz', 'value': 0.0},
            'presence': {'name': 'Presence', 'freq': '8kHz', 'value': 0.0}
        }
        
        # Détection de genre
        self.current_genre = "Unknown"
        self.genre_confidence = 0.0
        self.audio_level = 0.0
        
        # Contrôle volume système
        self.volume_control = None
        self.master_volume = 1.0
        
        # Configuration des routes
        self.setup_routes()
        self.setup_socketio()
        
        # Initialisation audio
        if AUDIO_AVAILABLE:
            self.setup_audio_system()

    def setup_routes(self):
        """Configuration des routes Flask"""
        
        @self.app.route('/')
        def index():
            return render_template('equalizer.html')
        
        @self.app.route('/api/devices')
        def get_devices():
            """Retourne la liste des périphériques audio"""
            return jsonify({
                'input_devices': self.audio_devices,
                'output_devices': self.output_devices,
                'selected_input': self.selected_input_device,
                'selected_output': self.selected_output_device
            })
        
        @self.app.route('/api/equalizer', methods=['GET', 'POST'])
        def equalizer_api():
            if request.method == 'GET':
                return jsonify({
                    'bands': self.eq_bands,
                    'master_volume': self.master_volume,
                    'is_listening': self.is_listening,
                    'current_genre': self.current_genre,
                    'genre_confidence': self.genre_confidence,
                    'audio_level': self.audio_level
                })
            
            elif request.method == 'POST':
                data = request.json
                
                if 'band' in data and 'value' in data:
                    # Mise à jour d'une bande
                    band = data['band']
                    value = float(data['value'])
                    if band in self.eq_bands:
                        self.eq_bands[band]['value'] = value
                        self.apply_eq_band(band, value)
                        return jsonify({'success': True})
                
                elif 'master_volume' in data:
                    # Mise à jour du volume maître
                    self.master_volume = float(data['master_volume'])
                    self.set_system_volume(self.master_volume)
                    return jsonify({'success': True})
                
                elif 'preset' in data:
                    # Application d'un preset
                    self.apply_preset(data['preset'])
                    return jsonify({'success': True, 'bands': self.eq_bands})
                
                elif 'auto_adjust' in data:
                    # Ajustement automatique
                    self.auto_adjust_equalizer()
                    return jsonify({'success': True, 'bands': self.eq_bands})
                
                return jsonify({'error': 'Invalid request'}), 400
        
        @self.app.route('/api/audio/start', methods=['POST'])
        def start_audio():
            data = request.json
            input_device = data.get('input_device')
            output_device = data.get('output_device')
            
            success = self.start_audio_capture(input_device, output_device)
            return jsonify({'success': success})
        
        @self.app.route('/api/audio/stop', methods=['POST'])
        def stop_audio():
            self.stop_audio_capture()
            return jsonify({'success': True})

    def setup_socketio(self):
        """Configuration des événements SocketIO"""
        
        @self.socketio.on('connect')
        def handle_connect():
            print(f"Client connecté: {request.sid}")
            emit('status', {
                'connected': True,
                'audio_available': AUDIO_AVAILABLE,
                'devices': {
                    'input_devices': self.audio_devices,
                    'output_devices': self.output_devices
                }
            })
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client déconnecté: {request.sid}")

    def setup_audio_system(self):
        """Initialisation du système audio"""
        try:
            # PyAudio pour la capture
            self.audio = pyaudio.PyAudio()
            self.scan_audio_devices()
            
            # Contrôle volume système Windows
            self.setup_volume_control()
            
            print("✅ Système audio initialisé")
            
        except Exception as e:
            print(f"❌ Erreur initialisation audio: {e}")
            self.audio = None

    def scan_audio_devices(self):
        """Scan des périphériques audio disponibles"""
        self.audio_devices = []
        self.output_devices = []
        
        try:
            for i in range(self.audio.get_device_count()):
                info = self.audio.get_device_info_by_index(i)
                
                device = {
                    'index': i,
                    'name': info['name'],
                    'channels': info['maxInputChannels'],
                    'sample_rate': int(info['defaultSampleRate'])
                }
                
                # Périphériques d'entrée (capture)
                if info['maxInputChannels'] > 0:
                    device['type'] = 'input'
                    self.audio_devices.append(device)
                    
                    # Sélection automatique de Stereo Mix
                    if ('stereo mix' in info['name'].lower() or 
                        'mixage' in info['name'].lower() or
                        'loopback' in info['name'].lower()):
                        self.selected_input_device = i
                
                # Périphériques de sortie
                if info['maxOutputChannels'] > 0:
                    device['type'] = 'output'
                    self.output_devices.append(device)
            
            # Sélection par défaut si pas de Stereo Mix
            if self.selected_input_device is None and self.audio_devices:
                self.selected_input_device = self.audio_devices[0]['index']
            
            if self.output_devices:
                self.selected_output_device = self.output_devices[0]['index']
                
            print(f"✅ {len(self.audio_devices)} périphériques d'entrée trouvés")
            print(f"✅ {len(self.output_devices)} périphériques de sortie trouvés")
            
        except Exception as e:
            print(f"❌ Erreur scan périphériques: {e}")

    def setup_volume_control(self):
        """Configuration du contrôle volume système"""
        try:
            devices = AudioUtilities.GetSpeakers()
            interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
            self.volume_control = interface.QueryInterface(IAudioEndpointVolume)
            
            # Volume actuel
            self.master_volume = self.volume_control.GetMasterScalarVolume()
            print("✅ Contrôle volume système activé")
            
        except Exception as e:
            print(f"⚠️ Contrôle volume non disponible: {e}")
            self.volume_control = None

    def start_audio_capture(self, input_device=None, output_device=None):
        """Démarre la capture audio"""
        if not AUDIO_AVAILABLE or not self.audio:
            return False
        
        try:
            if input_device is not None:
                self.selected_input_device = input_device
            if output_device is not None:
                self.selected_output_device = output_device
            
            # Configuration du stream
            self.stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.selected_input_device,
                frames_per_buffer=self.chunk_size
            )
            
            self.is_listening = True
            
            # Démarrer le thread d'analyse
            self.audio_thread = threading.Thread(target=self.audio_processing_loop, daemon=True)
            self.audio_thread.start()
            
            # Notifier les clients
            self.socketio.emit('audio_started', {
                'input_device': self.selected_input_device,
                'output_device': self.selected_output_device
            })
            
            print("🎧 Capture audio démarrée")
            return True
            
        except Exception as e:
            print(f"❌ Erreur démarrage capture: {e}")
            return False

    def stop_audio_capture(self):
        """Arrête la capture audio"""
        self.is_listening = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        
        self.socketio.emit('audio_stopped')
        print("⏹️ Capture audio arrêtée")

    def audio_processing_loop(self):
        """Boucle principale de traitement audio"""
        while self.is_listening:
            try:
                # Lecture des données audio
                data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.float32)
                
                # Calcul du niveau audio
                self.audio_level = float(np.sqrt(np.mean(audio_data**2)))
                
                # Analyse seulement si signal détecté
                if self.audio_level > 0.001:
                    self.analyze_audio(audio_data)
                
                # Envoi des données en temps réel
                self.socketio.emit('audio_data', {
                    'level': self.audio_level,
                    'genre': self.current_genre,
                    'confidence': self.genre_confidence,
                    'waveform': audio_data[::10].tolist()  # Sous-échantillonnage
                })
                
                time.sleep(0.05)  # 20 FPS
                
            except Exception as e:
                print(f"❌ Erreur traitement audio: {e}")
                break

    def analyze_audio(self, audio_data):
        """Analyse l'audio pour détecter le genre"""
        try:
            # FFT pour analyse spectrale
            fft = np.fft.fft(audio_data)
            freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
            magnitude = np.abs(fft)
            
            # Calcul des énergies par bandes
            bass_energy = np.mean(magnitude[(freqs >= 20) & (freqs <= 250)])
            mid_energy = np.mean(magnitude[(freqs >= 250) & (freqs <= 4000)])
            treble_energy = np.mean(magnitude[(freqs >= 4000) & (freqs <= 20000)])
            
            total_energy = bass_energy + mid_energy + treble_energy
            
            if total_energy > 0:
                bass_ratio = bass_energy / total_energy
                mid_ratio = mid_energy / total_energy
                treble_ratio = treble_energy / total_energy
                
                # Classification de genre
                genre, confidence = self.classify_genre(bass_ratio, mid_ratio, treble_ratio)
                
                # Mise à jour si changement significatif
                if genre != self.current_genre or abs(confidence - self.genre_confidence) > 5:
                    self.current_genre = genre
                    self.genre_confidence = confidence
                    
                    self.socketio.emit('genre_detected', {
                        'genre': genre,
                        'confidence': confidence
                    })
                    
        except Exception as e:
            print(f"❌ Erreur analyse: {e}")

    def classify_genre(self, bass_ratio, mid_ratio, treble_ratio):
        """Classification de genre basée sur les ratios de fréquences"""
        
        if bass_ratio > 0.4 and treble_ratio > 0.3:
            return "Rock", min(95, (bass_ratio + treble_ratio) * 120)
        elif bass_ratio > 0.5:
            return "Electronic", min(90, bass_ratio * 160)
        elif treble_ratio > 0.4:
            return "Classical", min(85, treble_ratio * 180)
        elif mid_ratio > 0.5:
            return "Jazz", min(80, mid_ratio * 140)
        elif bass_ratio > 0.3 and mid_ratio > 0.3:
            return "Pop", min(75, (bass_ratio + mid_ratio) * 100)
        else:
            return "Ambient", 60

    def apply_eq_band(self, band, value):
        """Applique le réglage d'une bande d'égalisation"""
        # Dans une vraie implémentation, on modifierait l'audio système ici
        print(f"🎛️ {band}: {value:+.1f}dB")
        
        # Notification temps réel
        self.socketio.emit('eq_changed', {
            'band': band,
            'value': value
        })

    def set_system_volume(self, volume):
        """Définit le volume système"""
        try:
            if self.volume_control:
                self.volume_control.SetMasterScalarVolume(volume, None)
                print(f"🔊 Volume système: {volume*100:.0f}%")
                
                self.socketio.emit('volume_changed', {
                    'volume': volume
                })
                
        except Exception as e:
            print(f"❌ Erreur volume: {e}")

    def apply_preset(self, preset_name):
        """Applique un preset d'égaliseur"""
        presets = {
            'flat': {'bass': 0, 'low_mid': 0, 'mid': 0, 'high_mid': 0, 'treble': 0, 'presence': 0},
            'rock': {'bass': 6, 'low_mid': 2, 'mid': -1, 'high_mid': 3, 'treble': 4, 'presence': 2},
            'pop': {'bass': 2, 'low_mid': 0, 'mid': 1, 'high_mid': 2, 'treble': 2, 'presence': 1},
            'electronic': {'bass': 8, 'low_mid': 3, 'mid': -2, 'high_mid': 1, 'treble': 5, 'presence': 3},
            'classical': {'bass': 0, 'low_mid': 0, 'mid': 0, 'high_mid': 1, 'treble': 2, 'presence': 1},
            'jazz': {'bass': 1, 'low_mid': 3, 'mid': 2, 'high_mid': 0, 'treble': 1, 'presence': 0},
            'vocal': {'bass': -2, 'low_mid': 1, 'mid': 3, 'high_mid': 4, 'treble': 2, 'presence': 3}
        }
        
        if preset_name in presets:
            preset = presets[preset_name]
            for band, value in preset.items():
                if band in self.eq_bands:
                    self.eq_bands[band]['value'] = value
                    self.apply_eq_band(band, value)
            
            print(f"⚡ Preset '{preset_name}' appliqué")

    def auto_adjust_equalizer(self):
        """Ajustement automatique basé sur le genre détecté"""
        genre_presets = {
            'Rock': 'rock',
            'Pop': 'pop',
            'Electronic': 'electronic',
            'Classical': 'classical',
            'Jazz': 'jazz',
            'Ambient': 'vocal'
        }
        
        preset = genre_presets.get(self.current_genre, 'flat')
        self.apply_preset(preset)
        
        self.socketio.emit('auto_adjusted', {
            'genre': self.current_genre,
            'preset': preset,
            'bands': self.eq_bands
        })

    def run(self, host='127.0.0.1', port=5000, debug=False):
        """Lance l'application"""
        print(f"🚀 AI Equalizer Moderne démarré")
        print(f"🌐 Interface web: http://{host}:{port}")
        print(f"🎛️ Périphériques audio: {len(self.audio_devices)} entrées, {len(self.output_devices)} sorties")
        
        self.socketio.run(self.app, host=host, port=port, debug=debug)

    def cleanup(self):
        """Nettoyage à la fermeture"""
        if self.is_listening:
            self.stop_audio_capture()
        
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()

if __name__ == "__main__":
    app = ModernEqualizerApp()
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Arrêt de l'application...")
        app.cleanup()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        app.cleanup()
